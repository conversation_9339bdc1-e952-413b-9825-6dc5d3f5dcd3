# نظام الفواتير
# Receipt system for supermarket

from utils import *
from config import *
import os

class ReceiptGenerator:
    def __init__(self, customer_manager):
        self.customer_manager = customer_manager

    def generate_receipt_text(self, sale):
        """توليد نص الفاتورة"""
        receipt_lines = []
        
        # رأس الفاتورة
        receipt_lines.append(RECEIPT_HEADER)
        receipt_lines.append(f"رقم الفاتورة: {sale.id}")
        receipt_lines.append(f"التاريخ: {sale.created_at}")
        
        if sale.cashier_name:
            receipt_lines.append(f"الكاشير: {sale.cashier_name}")
        
        # معلومات العميل
        if sale.customer_id:
            customer = self.customer_manager.get_customer(sale.customer_id)
            if customer:
                receipt_lines.append(f"العميل: {customer.name}")
                receipt_lines.append(f"نوع العميل: {customer.customer_type}")
                if customer.phone:
                    receipt_lines.append(f"الهاتف: {customer.phone}")
        
        receipt_lines.append("=" * 40)
        
        # تفاصيل العناصر
        receipt_lines.append(f"{'المنتج':<20} {'الكمية':<6} {'السعر':<10} {'المجموع':<10}")
        receipt_lines.append("-" * 40)
        
        for item in sale.items:
            receipt_lines.append(
                f"{item.product_name[:19]:<20} "
                f"{item.quantity:<6} "
                f"{item.unit_price:<10.0f} "
                f"{item.subtotal:<10.0f}"
            )
            
            if item.discount > 0:
                receipt_lines.append(f"  خصم {item.discount}%")
        
        receipt_lines.append("-" * 40)
        
        # الإجماليات
        receipt_lines.append(f"المجموع الفرعي: {format_currency(sale.subtotal)}")
        
        if sale.discount_amount > 0:
            receipt_lines.append(f"الخصم: {format_currency(sale.discount_amount)}")
        
        if sale.tax_amount > 0:
            receipt_lines.append(f"الضريبة ({TAX_RATE}%): {format_currency(sale.tax_amount)}")
        
        if sale.points_used > 0:
            receipt_lines.append(f"النقاط المستخدمة: {sale.points_used}")
            receipt_lines.append(f"قيمة النقاط: {format_currency(sale.points_used)}")
        
        receipt_lines.append("=" * 40)
        receipt_lines.append(f"الإجمالي: {format_currency(sale.total_amount)}")
        receipt_lines.append(f"المبلغ المدفوع: {format_currency(sale.paid_amount)}")
        
        if sale.change_amount > 0:
            receipt_lines.append(f"الباقي: {format_currency(sale.change_amount)}")
        
        receipt_lines.append(f"طريقة الدفع: {sale.payment_method}")
        
        # النقاط المكتسبة
        if sale.points_earned > 0:
            receipt_lines.append(f"النقاط المكتسبة: {sale.points_earned}")
        
        # معلومات العميل بعد البيع
        if sale.customer_id:
            customer = self.customer_manager.get_customer(sale.customer_id)
            if customer:
                receipt_lines.append(f"إجمالي نقاطكم: {customer.points}")
                receipt_lines.append(f"إجمالي مشترياتكم: {format_currency(customer.total_purchases)}")
        
        receipt_lines.append(RECEIPT_FOOTER)
        
        return "\n".join(receipt_lines)

    def print_receipt(self, sale):
        """طباعة الفاتورة على الشاشة"""
        receipt_text = self.generate_receipt_text(sale)
        print(receipt_text)

    def save_receipt_to_file(self, sale, filename=None):
        """حفظ الفاتورة في ملف"""
        if filename is None:
            # إنشاء مجلد الفواتير
            receipts_dir = os.path.join(DATABASE_DIR, "receipts")
            if not os.path.exists(receipts_dir):
                os.makedirs(receipts_dir)
            
            # اسم الملف بناءً على رقم الفاتورة والتاريخ
            date_str = sale.created_at[:10].replace("-", "")
            filename = os.path.join(receipts_dir, f"receipt_{sale.id}_{date_str}.txt")
        
        receipt_text = self.generate_receipt_text(sale)
        
        try:
            with open(filename, 'w', encoding='utf-8') as file:
                file.write(receipt_text)
            return filename
        except Exception as e:
            raise Exception(f"خطأ في حفظ الفاتورة: {e}")

    def generate_daily_report(self, sales_list, date_str):
        """توليد تقرير يومي"""
        if not sales_list:
            return "لا توجد مبيعات في هذا التاريخ"
        
        report_lines = []
        report_lines.append("=" * 50)
        report_lines.append(f"تقرير المبيعات اليومي - {date_str}")
        report_lines.append("=" * 50)
        
        # إحصائيات عامة
        total_sales = sum(sale.total_amount for sale in sales_list)
        total_items = sum(len(sale.items) for sale in sales_list)
        total_quantity = sum(sum(item.quantity for item in sale.items) for sale in sales_list)
        
        report_lines.append(f"عدد الفواتير: {len(sales_list)}")
        report_lines.append(f"إجمالي العناصر: {total_items}")
        report_lines.append(f"إجمالي الكمية: {total_quantity}")
        report_lines.append(f"إجمالي المبيعات: {format_currency(total_sales)}")
        
        # تفاصيل الفواتير
        report_lines.append("\n" + "=" * 50)
        report_lines.append("تفاصيل الفواتير")
        report_lines.append("=" * 50)
        report_lines.append(f"{'رقم الفاتورة':<12} {'الوقت':<10} {'الإجمالي':<15} {'طريقة الدفع':<12}")
        report_lines.append("-" * 50)
        
        for sale in sorted(sales_list, key=lambda s: s.created_at):
            time_str = sale.created_at[11:16]  # HH:MM
            report_lines.append(
                f"{sale.id:<12} {time_str:<10} "
                f"{format_currency(sale.total_amount):<15} {sale.payment_method:<12}"
            )
        
        # إحصائيات طرق الدفع
        payment_methods = {}
        for sale in sales_list:
            method = sale.payment_method
            if method not in payment_methods:
                payment_methods[method] = {'count': 0, 'total': 0}
            payment_methods[method]['count'] += 1
            payment_methods[method]['total'] += sale.total_amount
        
        if payment_methods:
            report_lines.append("\n" + "=" * 50)
            report_lines.append("إحصائيات طرق الدفع")
            report_lines.append("=" * 50)
            for method, stats in payment_methods.items():
                report_lines.append(
                    f"{method}: {stats['count']} فاتورة - {format_currency(stats['total'])}"
                )
        
        return "\n".join(report_lines)

    def save_daily_report(self, sales_list, date_str):
        """حفظ التقرير اليومي"""
        report_text = self.generate_daily_report(sales_list, date_str)
        
        # إنشاء مجلد التقارير
        reports_dir = os.path.join(DATABASE_DIR, "reports")
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        # اسم ملف التقرير
        filename = os.path.join(reports_dir, f"daily_report_{date_str.replace('-', '')}.txt")
        
        try:
            with open(filename, 'w', encoding='utf-8') as file:
                file.write(report_text)
            return filename
        except Exception as e:
            raise Exception(f"خطأ في حفظ التقرير: {e}")

    def generate_product_sales_report(self, sales_list, title="تقرير مبيعات المنتجات"):
        """توليد تقرير مبيعات المنتجات"""
        if not sales_list:
            return "لا توجد مبيعات للتقرير"
        
        # تجميع بيانات المنتجات
        product_stats = {}
        
        for sale in sales_list:
            for item in sale.items:
                product_id = item.product_id
                if product_id not in product_stats:
                    product_stats[product_id] = {
                        'name': item.product_name,
                        'quantity': 0,
                        'total_sales': 0,
                        'count': 0
                    }
                
                product_stats[product_id]['quantity'] += item.quantity
                product_stats[product_id]['total_sales'] += item.subtotal
                product_stats[product_id]['count'] += 1
        
        # ترتيب المنتجات حسب المبيعات
        sorted_products = sorted(
            product_stats.items(),
            key=lambda x: x[1]['total_sales'],
            reverse=True
        )
        
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append(title)
        report_lines.append("=" * 60)
        
        report_lines.append(f"{'المنتج':<25} {'الكمية':<10} {'المبيعات':<15} {'عدد الفواتير':<12}")
        report_lines.append("-" * 60)
        
        for product_id, stats in sorted_products:
            report_lines.append(
                f"{stats['name'][:24]:<25} "
                f"{stats['quantity']:<10} "
                f"{format_currency(stats['total_sales']):<15} "
                f"{stats['count']:<12}"
            )
        
        # إجماليات
        total_quantity = sum(stats['quantity'] for _, stats in product_stats.items())
        total_sales = sum(stats['total_sales'] for _, stats in product_stats.items())
        
        report_lines.append("-" * 60)
        report_lines.append(f"الإجمالي: {total_quantity} قطعة - {format_currency(total_sales)}")
        
        return "\n".join(report_lines)

    def generate_customer_sales_report(self, customer_sales, customer_name):
        """توليد تقرير مبيعات عميل"""
        if not customer_sales:
            return f"لا توجد مبيعات للعميل {customer_name}"
        
        report_lines = []
        report_lines.append("=" * 50)
        report_lines.append(f"تقرير مبيعات العميل: {customer_name}")
        report_lines.append("=" * 50)
        
        total_amount = sum(sale.total_amount for sale in customer_sales)
        total_items = sum(len(sale.items) for sale in customer_sales)
        
        report_lines.append(f"عدد الفواتير: {len(customer_sales)}")
        report_lines.append(f"إجمالي العناصر: {total_items}")
        report_lines.append(f"إجمالي المبيعات: {format_currency(total_amount)}")
        
        report_lines.append("\n" + "-" * 50)
        report_lines.append(f"{'رقم الفاتورة':<12} {'التاريخ':<20} {'الإجمالي':<15}")
        report_lines.append("-" * 50)
        
        for sale in sorted(customer_sales, key=lambda s: s.created_at, reverse=True):
            report_lines.append(
                f"{sale.id:<12} {sale.created_at[:16]:<20} "
                f"{format_currency(sale.total_amount):<15}"
            )
        
        return "\n".join(report_lines)
