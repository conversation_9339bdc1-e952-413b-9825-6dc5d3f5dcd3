# دليل البدء السريع - برنامج إدارة السوبر ماركت
## Quick Start Guide - Supermarket Management System

## 🚀 البدء السريع

### 1. تشغيل البرنامج
```bash
# على Windows
start.bat

# على Linux/Mac
./start.sh

# أو مباشرة
python run.py
```

### 2. إنشاء بيانات تجريبية (للمبتدئين)
```bash
python sample_data.py
```

## 📋 المهام الأساسية

### إضافة منتج جديد
1. القائمة الرئيسية → **1. إدارة المنتجات**
2. **3. إضافة منتج جديد**
3. أدخل البيانات:
   - الاسم: `تفاح أحمر`
   - الباركود: `123456` (اختياري)
   - الفئة: `فواكه`
   - سعر البيع: `2500`
   - سعر التكلفة: `2000`
   - المورد: `مزارع العراق`

### إضافة عميل جديد
1. القائمة الرئيسية → **3. إدارة العملاء**
2. **3. إضافة عميل جديد**
3. أدخل البيانات:
   - الاسم: `أحمد محمد`
   - الهاتف: `07901234567`
   - النوع: `1` (عادي)

### إجراء عملية بيع
1. القائمة الرئيسية → **4. المبيعات**
2. **1. بيع جديد**
3. ربط بعميل (اختياري): `y` ثم أدخل رقم الهاتف
4. إضافة منتجات:
   - **1. إضافة منتج**
   - أدخل معرف المنتج أو الباركود
   - أدخل الكمية
5. **0. العودة للقائمة الرئيسية**
6. **3. إتمام البيع**
7. اختر طريقة الدفع وأدخل المبلغ

### عرض التقارير
1. القائمة الرئيسية → **5. التقارير**
2. اختر نوع التقرير:
   - **1. تقرير المبيعات اليومي**
   - **4. تقرير أفضل العملاء**
   - **6. إحصائيات عامة**

## 🔧 نصائح مفيدة

### البحث السريع
- **في المنتجات**: يمكن البحث بالاسم، الباركود، الفئة، أو المورد
- **في العملاء**: يمكن البحث بالاسم، الهاتف، البريد، أو النوع

### اختصارات لوحة المفاتيح
- **0**: العودة للقائمة السابقة
- **Enter**: تأكيد الإدخال
- **Ctrl+C**: إنهاء البرنامج

### إدارة المخزون
- تحديد **الحد الأدنى** لتجنب نفاد المخزون
- تحديد **نقطة إعادة الطلب** للتنبيه المبكر
- مراجعة **تقرير المخزون المنخفض** بانتظام

### نظام النقاط
- **1000 دينار = 1 نقطة**
- **1 نقطة = 1 دينار خصم**
- النقاط تتراكم تلقائياً مع كل عملية شراء

## 📊 أمثلة عملية

### مثال: إضافة منتج مع مخزون
```
1. إدارة المنتجات → إضافة منتج جديد
   - الاسم: خبز عربي
   - السعر: 500
   - التكلفة: 300

2. إضافة للمخزون؟ y
   - الكمية: 100
   - الحد الأدنى: 20
   - نقطة إعادة الطلب: 30
```

### مثال: بيع لعميل VIP
```
1. المبيعات → بيع جديد
2. ربط بعميل؟ y → 07901234567
3. إضافة منتج → 1 (معرف المنتج) → 2 (الكمية)
4. إتمام البيع → نقدي → 5000 (المبلغ المدفوع)
```

## ⚠️ تحذيرات مهمة

1. **النسخ الاحتياطي**: احفظ مجلد `data/` بانتظام
2. **الأرقام**: استخدم الأرقام الإنجليزية فقط
3. **الباركود**: تأكد من عدم تكرار الباركود
4. **المخزون**: تحقق من المخزون قبل البيع

## 🆘 حل المشاكل الشائعة

### "المنتج غير موجود"
- تأكد من إضافة المنتج أولاً
- تحقق من صحة المعرف أو الباركود

### "المخزون غير كافي"
- أضف مخزون: إدارة المخزون → إضافة مخزون
- تحقق من الكمية المتاحة

### "العميل غير موجود"
- أضف العميل: إدارة العملاء → إضافة عميل جديد
- تحقق من رقم الهاتف

### البرنامج لا يعمل
```bash
# تحقق من المتطلبات
python --version

# اختبار البرنامج
python test_program.py
```

## 📞 الدعم

للحصول على مساعدة إضافية:
1. راجع ملف `README.md` للتفاصيل الكاملة
2. شغل `python test_program.py` لاختبار النظام
3. تحقق من مجلد `data/` للبيانات المحفوظة

---
**نصيحة**: ابدأ بإنشاء بيانات تجريبية باستخدام `python sample_data.py` لتجربة جميع الميزات!
