# إدارة المخزون
# Inventory management for supermarket system

from utils import *
from config import *

class InventoryItem:
    def __init__(self, product_id, quantity=0, min_stock=0, max_stock=0, 
                 reorder_point=0, location=""):
        self.product_id = product_id
        self.quantity = quantity
        self.min_stock = min_stock
        self.max_stock = max_stock
        self.reorder_point = reorder_point
        self.location = location
        self.last_updated = get_current_timestamp()

    def to_dict(self):
        """تحويل عنصر المخزون إلى قاموس"""
        return {
            'product_id': self.product_id,
            'quantity': self.quantity,
            'min_stock': self.min_stock,
            'max_stock': self.max_stock,
            'reorder_point': self.reorder_point,
            'location': self.location,
            'last_updated': self.last_updated
        }

    @classmethod
    def from_dict(cls, data):
        """إنشاء عنصر مخزون من قاموس"""
        item = cls(
            product_id=data.get('product_id'),
            quantity=data.get('quantity', 0),
            min_stock=data.get('min_stock', 0),
            max_stock=data.get('max_stock', 0),
            reorder_point=data.get('reorder_point', 0),
            location=data.get('location', '')
        )
        item.last_updated = data.get('last_updated', get_current_timestamp())
        return item

    def is_low_stock(self):
        """التحقق من انخفاض المخزون"""
        return self.quantity <= self.reorder_point

    def is_out_of_stock(self):
        """التحقق من نفاد المخزون"""
        return self.quantity <= 0

    def update_quantity(self, new_quantity):
        """تحديث الكمية"""
        self.quantity = new_quantity
        self.last_updated = get_current_timestamp()

    def add_stock(self, quantity):
        """إضافة مخزون"""
        self.quantity += quantity
        self.last_updated = get_current_timestamp()

    def remove_stock(self, quantity):
        """إزالة مخزون"""
        if quantity > self.quantity:
            raise ValueError("الكمية المطلوبة أكبر من المخزون المتاح")
        self.quantity -= quantity
        self.last_updated = get_current_timestamp()

class InventoryManager:
    def __init__(self, product_manager):
        self.product_manager = product_manager
        self.inventory = {}
        self.load_inventory()

    def load_inventory(self):
        """تحميل المخزون من الملف"""
        data = load_json_file(INVENTORY_FILE)
        self.inventory = {}
        for product_id, item_data in data.items():
            item = InventoryItem.from_dict(item_data)
            self.inventory[int(product_id)] = item

    def save_inventory(self):
        """حفظ المخزون في الملف"""
        data = {}
        for product_id, item in self.inventory.items():
            data[str(product_id)] = item.to_dict()
        save_json_file(INVENTORY_FILE, data)

    def add_product_to_inventory(self, product_id, quantity=0, min_stock=0, 
                                max_stock=0, reorder_point=0, location=""):
        """إضافة منتج للمخزون"""
        if not self.product_manager.get_product(product_id):
            raise ValueError("المنتج غير موجود")
        
        if product_id in self.inventory:
            raise ValueError("المنتج موجود في المخزون مسبقاً")
        
        item = InventoryItem(product_id, quantity, min_stock, max_stock, 
                           reorder_point, location)
        self.inventory[product_id] = item
        self.save_inventory()

    def update_inventory_item(self, product_id, **kwargs):
        """تحديث عنصر المخزون"""
        if product_id not in self.inventory:
            raise ValueError("المنتج غير موجود في المخزون")
        
        item = self.inventory[product_id]
        for key, value in kwargs.items():
            if hasattr(item, key):
                setattr(item, key, value)
        
        item.last_updated = get_current_timestamp()
        self.save_inventory()

    def add_stock(self, product_id, quantity, note=""):
        """إضافة مخزون"""
        if product_id not in self.inventory:
            # إضافة المنتج للمخزون إذا لم يكن موجوداً
            self.add_product_to_inventory(product_id, quantity)
        else:
            self.inventory[product_id].add_stock(quantity)
            self.save_inventory()

    def remove_stock(self, product_id, quantity):
        """إزالة مخزون"""
        if product_id not in self.inventory:
            raise ValueError("المنتج غير موجود في المخزون")
        
        self.inventory[product_id].remove_stock(quantity)
        self.save_inventory()

    def get_stock_quantity(self, product_id):
        """الحصول على كمية المخزون"""
        if product_id not in self.inventory:
            return 0
        return self.inventory[product_id].quantity

    def check_stock_availability(self, product_id, required_quantity):
        """التحقق من توفر المخزون"""
        available_quantity = self.get_stock_quantity(product_id)
        return available_quantity >= required_quantity

    def get_low_stock_items(self):
        """الحصول على المنتجات منخفضة المخزون"""
        low_stock_items = []
        for product_id, item in self.inventory.items():
            if item.is_low_stock():
                product = self.product_manager.get_product(product_id)
                if product:
                    low_stock_items.append((product, item))
        return low_stock_items

    def get_out_of_stock_items(self):
        """الحصول على المنتجات نافدة المخزون"""
        out_of_stock_items = []
        for product_id, item in self.inventory.items():
            if item.is_out_of_stock():
                product = self.product_manager.get_product(product_id)
                if product:
                    out_of_stock_items.append((product, item))
        return out_of_stock_items

    def get_inventory_value(self):
        """حساب قيمة المخزون الإجمالية"""
        total_value = 0
        for product_id, item in self.inventory.items():
            product = self.product_manager.get_product(product_id)
            if product:
                total_value += product.cost * item.quantity
        return total_value

    def display_inventory(self, page=1):
        """عرض المخزون"""
        inventory_list = []
        for product_id, item in self.inventory.items():
            product = self.product_manager.get_product(product_id)
            if product:
                inventory_list.append((product, item))
        
        if not inventory_list:
            print("لا توجد منتجات في المخزون")
            return
        
        paginated = paginate_items(inventory_list, page)
        
        print(f"\nالمخزون (الصفحة {paginated['current_page']} من {paginated['total_pages']})")
        print(f"إجمالي المنتجات: {paginated['total_items']}")
        print(f"قيمة المخزون الإجمالية: {format_currency(self.get_inventory_value())}")
        print_separator()
        
        print(f"{'المعرف':<5} {'الاسم':<20} {'الكمية':<10} {'الحد الأدنى':<12} {'الموقع':<15} {'الحالة':<10}")
        print_separator()
        
        for product, item in paginated['items']:
            status = "نافد" if item.is_out_of_stock() else "منخفض" if item.is_low_stock() else "متوفر"
            print(f"{product.id:<5} {product.name:<20} {item.quantity:<10} "
                  f"{item.reorder_point:<12} {item.location:<15} {status:<10}")
        
        if paginated['total_pages'] > 1:
            print(f"\nالصفحة {paginated['current_page']} من {paginated['total_pages']}")

    def display_low_stock_report(self):
        """عرض تقرير المنتجات منخفضة المخزون"""
        low_stock_items = self.get_low_stock_items()
        
        if not low_stock_items:
            print("لا توجد منتجات منخفضة المخزون")
            return
        
        print_header("تقرير المنتجات منخفضة المخزون")
        print(f"{'المعرف':<5} {'الاسم':<20} {'الكمية الحالية':<15} {'نقطة إعادة الطلب':<18}")
        print_separator()
        
        for product, item in low_stock_items:
            print(f"{product.id:<5} {product.name:<20} {item.quantity:<15} {item.reorder_point:<18}")

    def display_inventory_details(self, product_id):
        """عرض تفاصيل المخزون لمنتج معين"""
        if product_id not in self.inventory:
            print("المنتج غير موجود في المخزون")
            return
        
        product = self.product_manager.get_product(product_id)
        item = self.inventory[product_id]
        
        if not product:
            print("بيانات المنتج غير متوفرة")
            return
        
        print_header("تفاصيل المخزون")
        print(f"المنتج: {product.name}")
        print(f"الكمية الحالية: {item.quantity}")
        print(f"الحد الأدنى: {item.min_stock}")
        print(f"الحد الأقصى: {item.max_stock}")
        print(f"نقطة إعادة الطلب: {item.reorder_point}")
        print(f"الموقع: {item.location}")
        print(f"قيمة المخزون: {format_currency(product.cost * item.quantity)}")
        print(f"آخر تحديث: {item.last_updated}")
        
        if item.is_out_of_stock():
            print("⚠️ تحذير: المنتج نافد من المخزون")
        elif item.is_low_stock():
            print("⚠️ تحذير: المخزون منخفض")
