# وظائف مساعدة لبرنامج السوبر ماركت
# Utility functions for supermarket program

import os
import json
from datetime import datetime
from config import *

def create_data_directory():
    """إنشاء مجلد البيانات إذا لم يكن موجوداً"""
    if not os.path.exists(DATABASE_DIR):
        os.makedirs(DATABASE_DIR)

def load_json_file(filename):
    """تحميل ملف JSON"""
    filepath = os.path.join(DATABASE_DIR, filename)
    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            return json.load(file)
    except FileNotFoundError:
        return {}
    except json.JSONDecodeError:
        return {}

def save_json_file(filename, data):
    """حفظ البيانات في ملف JSON"""
    create_data_directory()
    filepath = os.path.join(DATABASE_DIR, filename)
    with open(filepath, 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=2)

def format_currency(amount):
    """تنسيق المبلغ بالعملة العراقية"""
    return f"{amount:,.0f} {CURRENCY_SYMBOL}"

def get_current_timestamp():
    """الحصول على الوقت الحالي"""
    return datetime.now().strftime(DATE_FORMAT)

def validate_positive_number(value, field_name):
    """التحقق من صحة الرقم الموجب"""
    try:
        num = float(value)
        if num < 0:
            raise ValueError(f"{field_name} يجب أن يكون رقماً موجباً")
        return num
    except ValueError:
        raise ValueError(f"{field_name} يجب أن يكون رقماً صحيحاً")

def validate_positive_integer(value, field_name):
    """التحقق من صحة الرقم الصحيح الموجب"""
    try:
        num = int(value)
        if num < 0:
            raise ValueError(f"{field_name} يجب أن يكون رقماً صحيحاً موجباً")
        return num
    except ValueError:
        raise ValueError(f"{field_name} يجب أن يكون رقماً صحيحاً")

def clear_screen():
    """مسح الشاشة"""
    os.system('cls' if os.name == 'nt' else 'clear')

def pause():
    """إيقاف مؤقت"""
    input("\nاضغط Enter للمتابعة...")

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*50)
    print(f"  {title}")
    print("="*50)

def print_separator():
    """طباعة خط فاصل"""
    print("-" * 50)

def get_user_input(prompt, input_type=str, required=True):
    """الحصول على مدخلات المستخدم مع التحقق"""
    while True:
        try:
            value = input(f"{prompt}: ").strip()
            
            if not value and required:
                print("هذا الحقل مطلوب!")
                continue
            
            if not value and not required:
                return None
            
            if input_type == int:
                return validate_positive_integer(value, prompt)
            elif input_type == float:
                return validate_positive_number(value, prompt)
            else:
                return value
                
        except ValueError as e:
            print(f"خطأ: {e}")
        except KeyboardInterrupt:
            print("\nتم إلغاء العملية")
            return None

def display_menu(title, options):
    """عرض قائمة الخيارات"""
    print_header(title)
    for i, option in enumerate(options, 1):
        print(f"{i}. {option}")
    print("0. العودة للقائمة الرئيسية")
    print_separator()

def generate_id(existing_ids):
    """توليد معرف جديد"""
    if not existing_ids:
        return 1
    return max(existing_ids) + 1

def search_items(items, search_term, search_fields):
    """البحث في العناصر"""
    results = []
    search_term = search_term.lower()
    
    for item in items:
        for field in search_fields:
            if field in item and search_term in str(item[field]).lower():
                results.append(item)
                break
    
    return results

def paginate_items(items, page=1, items_per_page=ITEMS_PER_PAGE):
    """تقسيم العناصر إلى صفحات"""
    start_index = (page - 1) * items_per_page
    end_index = start_index + items_per_page
    
    total_pages = (len(items) + items_per_page - 1) // items_per_page
    
    return {
        'items': items[start_index:end_index],
        'current_page': page,
        'total_pages': total_pages,
        'total_items': len(items)
    }
