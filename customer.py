# فئة العملاء
# Customer class for supermarket system

from utils import *
from config import *

class Customer:
    def __init__(self, customer_id=None, name="", phone="", email="", 
                 address="", customer_type="عادي", discount_rate=0.0):
        self.id = customer_id
        self.name = name
        self.phone = phone
        self.email = email
        self.address = address
        self.customer_type = customer_type  # عادي، VIP، جملة
        self.discount_rate = discount_rate  # معدل الخصم
        self.total_purchases = 0.0
        self.points = 0
        self.created_at = get_current_timestamp()
        self.last_purchase = None

    def to_dict(self):
        """تحويل العميل إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'customer_type': self.customer_type,
            'discount_rate': self.discount_rate,
            'total_purchases': self.total_purchases,
            'points': self.points,
            'created_at': self.created_at,
            'last_purchase': self.last_purchase
        }

    @classmethod
    def from_dict(cls, data):
        """إنشاء عميل من قاموس"""
        customer = cls()
        customer.id = data.get('id')
        customer.name = data.get('name', '')
        customer.phone = data.get('phone', '')
        customer.email = data.get('email', '')
        customer.address = data.get('address', '')
        customer.customer_type = data.get('customer_type', 'عادي')
        customer.discount_rate = data.get('discount_rate', 0.0)
        customer.total_purchases = data.get('total_purchases', 0.0)
        customer.points = data.get('points', 0)
        customer.created_at = data.get('created_at', get_current_timestamp())
        customer.last_purchase = data.get('last_purchase')
        return customer

    def add_purchase(self, amount):
        """إضافة مشتريات للعميل"""
        self.total_purchases += amount
        self.last_purchase = get_current_timestamp()
        # إضافة نقاط (نقطة واحدة لكل 1000 دينار)
        self.points += int(amount / 1000)

    def use_points(self, points):
        """استخدام النقاط"""
        if points > self.points:
            raise ValueError("النقاط المطلوبة أكبر من النقاط المتاحة")
        self.points -= points

    def get_discount_amount(self, amount):
        """حساب مبلغ الخصم"""
        return amount * (self.discount_rate / 100)

    def __str__(self):
        return f"{self.name} ({self.customer_type})"

class CustomerManager:
    def __init__(self):
        self.customers = {}
        self.load_customers()

    def load_customers(self):
        """تحميل العملاء من الملف"""
        data = load_json_file(CUSTOMERS_FILE)
        self.customers = {}
        for customer_data in data.values():
            customer = Customer.from_dict(customer_data)
            self.customers[customer.id] = customer

    def save_customers(self):
        """حفظ العملاء في الملف"""
        data = {}
        for customer_id, customer in self.customers.items():
            data[str(customer_id)] = customer.to_dict()
        save_json_file(CUSTOMERS_FILE, data)

    def add_customer(self, customer):
        """إضافة عميل جديد"""
        if customer.id is None:
            customer.id = generate_id(list(self.customers.keys()))
        
        # التحقق من عدم تكرار رقم الهاتف
        if customer.phone and self.get_customer_by_phone(customer.phone):
            raise ValueError("رقم الهاتف موجود مسبقاً")
        
        self.customers[customer.id] = customer
        self.save_customers()
        return customer.id

    def update_customer(self, customer_id, **kwargs):
        """تحديث عميل"""
        if customer_id not in self.customers:
            raise ValueError("العميل غير موجود")
        
        # التحقق من رقم الهاتف عند التحديث
        if 'phone' in kwargs and kwargs['phone']:
            existing_customer = self.get_customer_by_phone(kwargs['phone'])
            if existing_customer and existing_customer.id != customer_id:
                raise ValueError("رقم الهاتف موجود مسبقاً")
        
        customer = self.customers[customer_id]
        for key, value in kwargs.items():
            if hasattr(customer, key):
                setattr(customer, key, value)
        
        self.save_customers()

    def delete_customer(self, customer_id):
        """حذف عميل"""
        if customer_id not in self.customers:
            raise ValueError("العميل غير موجود")
        
        del self.customers[customer_id]
        self.save_customers()

    def get_customer(self, customer_id):
        """الحصول على عميل بالمعرف"""
        return self.customers.get(customer_id)

    def get_customer_by_phone(self, phone):
        """الحصول على عميل برقم الهاتف"""
        for customer in self.customers.values():
            if customer.phone == phone:
                return customer
        return None

    def search_customers(self, search_term):
        """البحث في العملاء"""
        customers_list = list(self.customers.values())
        search_fields = ['name', 'phone', 'email', 'customer_type']
        return search_items(customers_list, search_term, search_fields)

    def get_customers_by_type(self, customer_type):
        """الحصول على العملاء حسب النوع"""
        return [c for c in self.customers.values() if c.customer_type == customer_type]

    def get_all_customers(self):
        """الحصول على جميع العملاء"""
        return list(self.customers.values())

    def get_top_customers(self, limit=10):
        """الحصول على أفضل العملاء حسب المشتريات"""
        customers = sorted(self.customers.values(), 
                         key=lambda c: c.total_purchases, reverse=True)
        return customers[:limit]

    def display_customers(self, customers=None, page=1):
        """عرض العملاء"""
        if customers is None:
            customers = self.get_all_customers()
        
        if not customers:
            print("لا يوجد عملاء للعرض")
            return
        
        paginated = paginate_items(customers, page)
        
        print(f"\nالعملاء (الصفحة {paginated['current_page']} من {paginated['total_pages']})")
        print(f"إجمالي العملاء: {paginated['total_items']}")
        print_separator()
        
        print(f"{'المعرف':<5} {'الاسم':<20} {'الهاتف':<15} {'النوع':<10} {'إجمالي المشتريات':<20}")
        print_separator()
        
        for customer in paginated['items']:
            print(f"{customer.id:<5} {customer.name:<20} {customer.phone:<15} "
                  f"{customer.customer_type:<10} {format_currency(customer.total_purchases):<20}")
        
        if paginated['total_pages'] > 1:
            print(f"\nالصفحة {paginated['current_page']} من {paginated['total_pages']}")

    def display_customer_details(self, customer):
        """عرض تفاصيل العميل"""
        print_header("تفاصيل العميل")
        print(f"المعرف: {customer.id}")
        print(f"الاسم: {customer.name}")
        print(f"الهاتف: {customer.phone}")
        print(f"البريد الإلكتروني: {customer.email}")
        print(f"العنوان: {customer.address}")
        print(f"نوع العميل: {customer.customer_type}")
        print(f"معدل الخصم: {customer.discount_rate}%")
        print(f"إجمالي المشتريات: {format_currency(customer.total_purchases)}")
        print(f"النقاط: {customer.points}")
        print(f"تاريخ التسجيل: {customer.created_at}")
        if customer.last_purchase:
            print(f"آخر عملية شراء: {customer.last_purchase}")

    def display_top_customers_report(self, limit=10):
        """عرض تقرير أفضل العملاء"""
        top_customers = self.get_top_customers(limit)
        
        if not top_customers:
            print("لا يوجد عملاء")
            return
        
        print_header(f"أفضل {limit} عملاء")
        print(f"{'الترتيب':<8} {'الاسم':<20} {'إجمالي المشتريات':<20} {'النقاط':<10}")
        print_separator()
        
        for i, customer in enumerate(top_customers, 1):
            print(f"{i:<8} {customer.name:<20} "
                  f"{format_currency(customer.total_purchases):<20} {customer.points:<10}")

    def get_customer_statistics(self):
        """إحصائيات العملاء"""
        total_customers = len(self.customers)
        if total_customers == 0:
            return {
                'total_customers': 0,
                'total_purchases': 0,
                'average_purchase': 0,
                'vip_customers': 0,
                'regular_customers': 0
            }
        
        total_purchases = sum(c.total_purchases for c in self.customers.values())
        average_purchase = total_purchases / total_customers
        vip_customers = len([c for c in self.customers.values() if c.customer_type == 'VIP'])
        regular_customers = len([c for c in self.customers.values() if c.customer_type == 'عادي'])
        
        return {
            'total_customers': total_customers,
            'total_purchases': total_purchases,
            'average_purchase': average_purchase,
            'vip_customers': vip_customers,
            'regular_customers': regular_customers
        }
