#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل برنامج السوبر ماركت
Supermarket Program Launcher
"""

import os
import sys

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من إصدار Python
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # التحقق من وجود الملفات المطلوبة
    required_files = [
        'main.py', 'config.py', 'utils.py', 'product.py',
        'inventory.py', 'customer.py', 'sale.py', 'receipt.py'
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ الملف المطلوب غير موجود: {file}")
            return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏪 مرحباً بكم في برنامج إدارة السوبر ماركت")
    print("   Supermarket Management System")
    print("=" * 60)
    
    if not check_requirements():
        print("\n❌ فشل في التحقق من المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    try:
        print("\n🚀 بدء تشغيل البرنامج...")
        
        # استيراد وتشغيل البرنامج الرئيسي
        from main import SupermarketSystem
        
        print("✅ تم تحميل النظام بنجاح")
        print("\n" + "=" * 60)
        
        # تشغيل البرنامج
        system = SupermarketSystem()
        system.run()
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إنهاء البرنامج بواسطة المستخدم")
    except ImportError as e:
        print(f"\n❌ خطأ في استيراد الوحدات: {e}")
        print("تأكد من وجود جميع الملفات في نفس المجلد")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("يرجى التواصل مع فريق الدعم")
    
    print("\n👋 شكراً لاستخدام برنامج إدارة السوبر ماركت")
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
