# نظام المبيعات
# Sales system for supermarket

from utils import *
from config import *

class SaleItem:
    def __init__(self, product_id, product_name, quantity, unit_price, discount=0.0):
        self.product_id = product_id
        self.product_name = product_name
        self.quantity = quantity
        self.unit_price = unit_price
        self.discount = discount  # خصم على العنصر
        self.subtotal = self.calculate_subtotal()

    def calculate_subtotal(self):
        """حساب المجموع الفرعي"""
        subtotal = self.quantity * self.unit_price
        discount_amount = subtotal * (self.discount / 100)
        return subtotal - discount_amount

    def to_dict(self):
        """تحويل عنصر البيع إلى قاموس"""
        return {
            'product_id': self.product_id,
            'product_name': self.product_name,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'discount': self.discount,
            'subtotal': self.subtotal
        }

    @classmethod
    def from_dict(cls, data):
        """إنشاء عنصر بيع من قاموس"""
        return cls(
            product_id=data.get('product_id'),
            product_name=data.get('product_name', ''),
            quantity=data.get('quantity', 0),
            unit_price=data.get('unit_price', 0.0),
            discount=data.get('discount', 0.0)
        )

class Sale:
    def __init__(self, sale_id=None, customer_id=None, cashier_name=""):
        self.id = sale_id
        self.customer_id = customer_id
        self.cashier_name = cashier_name
        self.items = []
        self.subtotal = 0.0
        self.discount_amount = 0.0
        self.tax_amount = 0.0
        self.total_amount = 0.0
        self.paid_amount = 0.0
        self.change_amount = 0.0
        self.payment_method = "نقدي"  # نقدي، بطاقة، نقاط
        self.points_used = 0
        self.points_earned = 0
        self.created_at = get_current_timestamp()
        self.status = "مكتملة"  # مكتملة، ملغية، معلقة

    def add_item(self, product_id, product_name, quantity, unit_price, discount=0.0):
        """إضافة عنصر للبيع"""
        # البحث عن العنصر إذا كان موجوداً
        for item in self.items:
            if item.product_id == product_id:
                item.quantity += quantity
                item.subtotal = item.calculate_subtotal()
                self.calculate_totals()
                return
        
        # إضافة عنصر جديد
        item = SaleItem(product_id, product_name, quantity, unit_price, discount)
        self.items.append(item)
        self.calculate_totals()

    def remove_item(self, product_id):
        """إزالة عنصر من البيع"""
        self.items = [item for item in self.items if item.product_id != product_id]
        self.calculate_totals()

    def update_item_quantity(self, product_id, new_quantity):
        """تحديث كمية عنصر"""
        for item in self.items:
            if item.product_id == product_id:
                if new_quantity <= 0:
                    self.remove_item(product_id)
                else:
                    item.quantity = new_quantity
                    item.subtotal = item.calculate_subtotal()
                    self.calculate_totals()
                break

    def apply_discount(self, discount_percentage):
        """تطبيق خصم على إجمالي الفاتورة"""
        self.discount_amount = self.subtotal * (discount_percentage / 100)
        self.calculate_totals()

    def calculate_totals(self):
        """حساب الإجماليات"""
        self.subtotal = sum(item.subtotal for item in self.items)
        
        # حساب الضريبة
        taxable_amount = self.subtotal - self.discount_amount
        self.tax_amount = taxable_amount * (TAX_RATE / 100)
        
        # حساب الإجمالي
        self.total_amount = taxable_amount + self.tax_amount
        
        # خصم النقاط المستخدمة (كل نقطة = 1 دينار)
        points_discount = self.points_used
        self.total_amount = max(0, self.total_amount - points_discount)

    def process_payment(self, paid_amount, payment_method="نقدي", points_used=0):
        """معالجة الدفع"""
        self.points_used = points_used
        self.payment_method = payment_method
        self.calculate_totals()
        
        self.paid_amount = paid_amount
        self.change_amount = max(0, paid_amount - self.total_amount)
        
        # حساب النقاط المكتسبة (نقطة لكل 1000 دينار)
        self.points_earned = int(self.total_amount / 1000)

    def to_dict(self):
        """تحويل البيع إلى قاموس"""
        return {
            'id': self.id,
            'customer_id': self.customer_id,
            'cashier_name': self.cashier_name,
            'items': [item.to_dict() for item in self.items],
            'subtotal': self.subtotal,
            'discount_amount': self.discount_amount,
            'tax_amount': self.tax_amount,
            'total_amount': self.total_amount,
            'paid_amount': self.paid_amount,
            'change_amount': self.change_amount,
            'payment_method': self.payment_method,
            'points_used': self.points_used,
            'points_earned': self.points_earned,
            'created_at': self.created_at,
            'status': self.status
        }

    @classmethod
    def from_dict(cls, data):
        """إنشاء بيع من قاموس"""
        sale = cls(
            sale_id=data.get('id'),
            customer_id=data.get('customer_id'),
            cashier_name=data.get('cashier_name', '')
        )
        
        # تحميل العناصر
        for item_data in data.get('items', []):
            item = SaleItem.from_dict(item_data)
            sale.items.append(item)
        
        sale.subtotal = data.get('subtotal', 0.0)
        sale.discount_amount = data.get('discount_amount', 0.0)
        sale.tax_amount = data.get('tax_amount', 0.0)
        sale.total_amount = data.get('total_amount', 0.0)
        sale.paid_amount = data.get('paid_amount', 0.0)
        sale.change_amount = data.get('change_amount', 0.0)
        sale.payment_method = data.get('payment_method', 'نقدي')
        sale.points_used = data.get('points_used', 0)
        sale.points_earned = data.get('points_earned', 0)
        sale.created_at = data.get('created_at', get_current_timestamp())
        sale.status = data.get('status', 'مكتملة')
        
        return sale

class SalesManager:
    def __init__(self, product_manager, inventory_manager, customer_manager):
        self.product_manager = product_manager
        self.inventory_manager = inventory_manager
        self.customer_manager = customer_manager
        self.sales = {}
        self.current_sale = None
        self.load_sales()

    def load_sales(self):
        """تحميل المبيعات من الملف"""
        data = load_json_file(SALES_FILE)
        self.sales = {}
        for sale_data in data.values():
            sale = Sale.from_dict(sale_data)
            self.sales[sale.id] = sale

    def save_sales(self):
        """حفظ المبيعات في الملف"""
        data = {}
        for sale_id, sale in self.sales.items():
            data[str(sale_id)] = sale.to_dict()
        save_json_file(SALES_FILE, data)

    def start_new_sale(self, customer_id=None, cashier_name=""):
        """بدء عملية بيع جديدة"""
        sale_id = generate_id(list(self.sales.keys()))
        self.current_sale = Sale(sale_id, customer_id, cashier_name)
        return self.current_sale

    def add_item_to_current_sale(self, product_id, quantity):
        """إضافة عنصر للبيع الحالي"""
        if not self.current_sale:
            raise ValueError("لا توجد عملية بيع نشطة")
        
        product = self.product_manager.get_product(product_id)
        if not product:
            raise ValueError("المنتج غير موجود")
        
        # التحقق من توفر المخزون
        if not self.inventory_manager.check_stock_availability(product_id, quantity):
            available = self.inventory_manager.get_stock_quantity(product_id)
            raise ValueError(f"المخزون غير كافي. المتوفر: {available}")
        
        # تطبيق خصم العميل إذا وجد
        discount = 0.0
        if self.current_sale.customer_id:
            customer = self.customer_manager.get_customer(self.current_sale.customer_id)
            if customer:
                discount = customer.discount_rate
        
        self.current_sale.add_item(product_id, product.name, quantity, product.price, discount)

    def complete_current_sale(self, paid_amount, payment_method="نقدي", points_used=0):
        """إتمام البيع الحالي"""
        if not self.current_sale:
            raise ValueError("لا توجد عملية بيع نشطة")
        
        if not self.current_sale.items:
            raise ValueError("لا توجد عناصر في البيع")
        
        # معالجة الدفع
        self.current_sale.process_payment(paid_amount, payment_method, points_used)
        
        if paid_amount < self.current_sale.total_amount:
            raise ValueError("المبلغ المدفوع أقل من الإجمالي المطلوب")
        
        # تحديث المخزون
        for item in self.current_sale.items:
            self.inventory_manager.remove_stock(item.product_id, item.quantity)
        
        # تحديث بيانات العميل
        if self.current_sale.customer_id:
            customer = self.customer_manager.get_customer(self.current_sale.customer_id)
            if customer:
                customer.add_purchase(self.current_sale.total_amount)
                if points_used > 0:
                    customer.use_points(points_used)
                customer.points += self.current_sale.points_earned
                self.customer_manager.save_customers()
        
        # حفظ البيع
        self.sales[self.current_sale.id] = self.current_sale
        self.save_sales()
        
        completed_sale = self.current_sale
        self.current_sale = None
        return completed_sale

    def cancel_current_sale(self):
        """إلغاء البيع الحالي"""
        self.current_sale = None

    def get_sale(self, sale_id):
        """الحصول على بيع بالمعرف"""
        return self.sales.get(sale_id)

    def get_sales_by_date(self, date_str):
        """الحصول على المبيعات حسب التاريخ"""
        sales = []
        for sale in self.sales.values():
            if sale.created_at.startswith(date_str):
                sales.append(sale)
        return sales

    def get_sales_by_customer(self, customer_id):
        """الحصول على مبيعات عميل معين"""
        return [sale for sale in self.sales.values() if sale.customer_id == customer_id]

    def get_daily_sales_total(self, date_str=None):
        """حساب إجمالي مبيعات اليوم"""
        if date_str is None:
            date_str = get_current_timestamp()[:10]  # YYYY-MM-DD
        
        daily_sales = self.get_sales_by_date(date_str)
        return sum(sale.total_amount for sale in daily_sales)

    def display_current_sale(self):
        """عرض البيع الحالي"""
        if not self.current_sale:
            print("لا توجد عملية بيع نشطة")
            return
        
        print_header("البيع الحالي")
        
        if self.current_sale.customer_id:
            customer = self.customer_manager.get_customer(self.current_sale.customer_id)
            if customer:
                print(f"العميل: {customer.name} ({customer.customer_type})")
                print(f"النقاط المتاحة: {customer.points}")
                print_separator()
        
        if not self.current_sale.items:
            print("لا توجد عناصر في البيع")
            return
        
        print(f"{'#':<3} {'المنتج':<20} {'الكمية':<8} {'السعر':<12} {'الخصم':<8} {'المجموع':<12}")
        print_separator()
        
        for i, item in enumerate(self.current_sale.items, 1):
            print(f"{i:<3} {item.product_name:<20} {item.quantity:<8} "
                  f"{format_currency(item.unit_price):<12} {item.discount}%{'':<3} "
                  f"{format_currency(item.subtotal):<12}")
        
        print_separator()
        print(f"المجموع الفرعي: {format_currency(self.current_sale.subtotal)}")
        if self.current_sale.discount_amount > 0:
            print(f"الخصم: {format_currency(self.current_sale.discount_amount)}")
        if self.current_sale.tax_amount > 0:
            print(f"الضريبة: {format_currency(self.current_sale.tax_amount)}")
        if self.current_sale.points_used > 0:
            print(f"النقاط المستخدمة: {self.current_sale.points_used}")
        print(f"الإجمالي: {format_currency(self.current_sale.total_amount)}")

    def display_sales_report(self, start_date=None, end_date=None):
        """عرض تقرير المبيعات"""
        sales_list = list(self.sales.values())
        
        if start_date:
            sales_list = [s for s in sales_list if s.created_at >= start_date]
        if end_date:
            sales_list = [s for s in sales_list if s.created_at <= end_date]
        
        if not sales_list:
            print("لا توجد مبيعات في الفترة المحددة")
            return
        
        total_sales = sum(sale.total_amount for sale in sales_list)
        total_items = sum(len(sale.items) for sale in sales_list)
        
        print_header("تقرير المبيعات")
        print(f"عدد الفواتير: {len(sales_list)}")
        print(f"إجمالي العناصر: {total_items}")
        print(f"إجمالي المبيعات: {format_currency(total_sales)}")
        print_separator()
        
        print(f"{'رقم الفاتورة':<12} {'التاريخ':<20} {'العميل':<15} {'الإجمالي':<15}")
        print_separator()
        
        for sale in sorted(sales_list, key=lambda s: s.created_at, reverse=True)[:20]:
            customer_name = "زبون عادي"
            if sale.customer_id:
                customer = self.customer_manager.get_customer(sale.customer_id)
                if customer:
                    customer_name = customer.name
            
            print(f"{sale.id:<12} {sale.created_at[:16]:<20} "
                  f"{customer_name:<15} {format_currency(sale.total_amount):<15}")
