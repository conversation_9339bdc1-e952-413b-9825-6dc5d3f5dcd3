#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار برنامج السوبر ماركت
Test script for supermarket program
"""

def test_imports():
    """اختبار استيراد الوحدات"""
    try:
        print("🔄 اختبار استيراد الوحدات...")
        
        from config import CURRENCY, CURRENCY_SYMBOL, MESSAGES
        print(f"✅ تم استيراد الإعدادات - العملة: {CURRENCY_SYMBOL}")
        
        from utils import format_currency, get_current_timestamp
        print("✅ تم استيراد الوظائف المساعدة")
        
        from product import Product, ProductManager
        print("✅ تم استيراد وحدة المنتجات")
        
        from inventory import InventoryManager
        print("✅ تم استيراد وحدة المخزون")
        
        from customer import Customer, CustomerManager
        print("✅ تم استيراد وحدة العملاء")
        
        from sale import SalesManager
        print("✅ تم استيراد وحدة المبيعات")
        
        from receipt import ReceiptGenerator
        print("✅ تم استيراد وحدة الفواتير")
        
        from main import SupermarketSystem
        print("✅ تم استيراد النظام الرئيسي")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    try:
        print("\n🔄 اختبار الوظائف الأساسية...")
        
        from main import SupermarketSystem
        from product import Product
        from customer import Customer
        
        # إنشاء النظام
        system = SupermarketSystem()
        print("✅ تم إنشاء النظام بنجاح")
        
        # اختبار إنشاء منتج
        product = Product(
            name="تفاح أحمر",
            barcode="123456789",
            category="فواكه",
            price=2500,
            cost=2000,
            supplier="مزارع العراق"
        )
        print("✅ تم إنشاء منتج تجريبي")
        
        # اختبار إنشاء عميل
        customer = Customer(
            name="أحمد محمد",
            phone="07901234567",
            email="<EMAIL>",
            customer_type="عادي"
        )
        print("✅ تم إنشاء عميل تجريبي")
        
        # اختبار تنسيق العملة
        from utils import format_currency
        formatted_price = format_currency(2500)
        print(f"✅ تنسيق العملة: {formatted_price}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_data_persistence():
    """اختبار حفظ البيانات"""
    try:
        print("\n🔄 اختبار حفظ البيانات...")
        
        from utils import create_data_directory, save_json_file, load_json_file
        
        # إنشاء مجلد البيانات
        create_data_directory()
        print("✅ تم إنشاء مجلد البيانات")
        
        # اختبار حفظ وتحميل البيانات
        test_data = {"test": "بيانات تجريبية", "number": 123}
        save_json_file("test.json", test_data)
        print("✅ تم حفظ البيانات التجريبية")
        
        loaded_data = load_json_file("test.json")
        if loaded_data == test_data:
            print("✅ تم تحميل البيانات بنجاح")
        else:
            print("❌ خطأ في تحميل البيانات")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("🧪 اختبار برنامج إدارة السوبر ماركت")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # اختبار الاستيراد
    if test_imports():
        tests_passed += 1
    
    # اختبار الوظائف الأساسية
    if test_basic_functionality():
        tests_passed += 1
    
    # اختبار حفظ البيانات
    if test_data_persistence():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام.")
        print("\n📝 لتشغيل البرنامج:")
        print("   python main.py")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
