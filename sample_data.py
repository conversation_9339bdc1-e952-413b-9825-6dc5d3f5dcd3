#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء بيانات تجريبية لبرنامج السوبر ماركت
Sample data generator for supermarket program
"""

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    try:
        print("🔄 إنشاء بيانات تجريبية...")
        
        from main import SupermarketSystem
        from product import Product
        from customer import Customer
        
        # إنشاء النظام
        system = SupermarketSystem()
        
        # إضافة منتجات تجريبية
        sample_products = [
            {
                'name': 'تفاح أحمر',
                'barcode': '1001',
                'category': 'فواكه',
                'price': 2500,
                'cost': 2000,
                'supplier': 'مزارع العراق',
                'description': 'تفاح أحمر طازج'
            },
            {
                'name': 'خبز عربي',
                'barcode': '2001',
                'category': 'مخبوزات',
                'price': 500,
                'cost': 300,
                'supplier': 'مخبز الرافدين',
                'description': 'خبز عربي طازج'
            },
            {
                'name': 'حليب كامل الدسم',
                'barcode': '3001',
                'category': 'ألبان',
                'price': 1500,
                'cost': 1200,
                'supplier': 'مصنع الألبان العراقي',
                'description': 'حليب كامل الدسم 1 لتر'
            },
            {
                'name': 'أرز بسمتي',
                'barcode': '4001',
                'category': 'حبوب',
                'price': 3000,
                'cost': 2500,
                'supplier': 'شركة الحبوب',
                'description': 'أرز بسمتي فاخر 1 كيلو'
            },
            {
                'name': 'زيت دوار الشمس',
                'barcode': '5001',
                'category': 'زيوت',
                'price': 4000,
                'cost': 3500,
                'supplier': 'مصنع الزيوت',
                'description': 'زيت دوار الشمس 1 لتر'
            }
        ]
        
        print("📦 إضافة المنتجات...")
        for product_data in sample_products:
            product = Product(**product_data)
            product_id = system.product_manager.add_product(product)
            
            # إضافة المنتج للمخزون
            system.inventory_manager.add_product_to_inventory(
                product_id=product_id,
                quantity=50,
                min_stock=10,
                reorder_point=15,
                location=f"رف {product_id}"
            )
            
            print(f"  ✅ {product.name}")
        
        # إضافة عملاء تجريبيين
        sample_customers = [
            {
                'name': 'أحمد محمد علي',
                'phone': '07901234567',
                'email': '<EMAIL>',
                'address': 'بغداد - الكرادة',
                'customer_type': 'عادي',
                'discount_rate': 0
            },
            {
                'name': 'فاطمة حسن',
                'phone': '07801234567',
                'email': '<EMAIL>',
                'address': 'البصرة - المعقل',
                'customer_type': 'VIP',
                'discount_rate': 5
            },
            {
                'name': 'محمد عبدالله',
                'phone': '07701234567',
                'email': '<EMAIL>',
                'address': 'أربيل - المركز',
                'customer_type': 'جملة',
                'discount_rate': 10
            }
        ]
        
        print("\n👥 إضافة العملاء...")
        for customer_data in sample_customers:
            customer = Customer(**customer_data)
            customer_id = system.customer_manager.add_customer(customer)
            print(f"  ✅ {customer.name}")
        
        print("\n🎉 تم إنشاء البيانات التجريبية بنجاح!")
        print("\nالبيانات المضافة:")
        print(f"  📦 {len(sample_products)} منتجات")
        print(f"  👥 {len(sample_customers)} عملاء")
        print(f"  📊 مخزون لجميع المنتجات")
        
        print("\n💡 يمكنك الآن:")
        print("  1. تشغيل البرنامج: python main.py")
        print("  2. إجراء عمليات بيع تجريبية")
        print("  3. عرض التقارير")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🗃️  إنشاء بيانات تجريبية")
    print("=" * 50)
    
    choice = input("هل تريد إنشاء بيانات تجريبية؟ (y/n): ").strip().lower()
    
    if choice == 'y':
        if create_sample_data():
            print("\n✅ تم بنجاح!")
        else:
            print("\n❌ فشل في الإنشاء")
    else:
        print("تم الإلغاء")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
