

from config import *
from product import Product, ProductManager
from inventory import InventoryManager
from customer import Customer, CustomerManager
from sale import SalesManager
from receipt import ReceiptGenerator

class SupermarketSystem:
    def __init__(self):
        self.product_manager = ProductManager()
        self.customer_manager = CustomerManager()
        self.inventory_manager = InventoryManager(self.product_manager)
        self.sales_manager = SalesManager(
            self.product_manager, 
            self.inventory_manager, 
            self.customer_manager
        )
        self.receipt_generator = ReceiptGenerator(self.customer_manager)

    def run(self):
        """تشغيل البرنامج الرئيسي"""
        while True:
            try:
                clear_screen()
                self.show_main_menu()
                choice = input("اختر من القائمة: ").strip()
                
                if choice == '1':
                    self.products_menu()
                elif choice == '2':
                    self.inventory_menu()
                elif choice == '3':
                    self.customers_menu()
                elif choice == '4':
                    self.sales_menu()
                elif choice == '5':
                    self.reports_menu()
                elif choice == '0':
                    print(MESSAGES['goodbye'])
                    break
                else:
                    print(MESSAGES['invalid_choice'])
                    pause()
                    
            except KeyboardInterrupt:
                print("\n" + MESSAGES['goodbye'])
                break
            except Exception as e:
                print(f"خطأ: {e}")
                pause()

    def show_main_menu(self):
        """عرض القائمة الرئيسية"""
        print_header(MESSAGES['welcome'])
        print("1. إدارة المنتجات")
        print("2. إدارة المخزون")
        print("3. إدارة العملاء")
        print("4. المبيعات")
        print("5. التقارير")
        print("0. خروج")
        print_separator()

    def products_menu(self):
        """قائمة إدارة المنتجات"""
        while True:
            clear_screen()
            display_menu("إدارة المنتجات", [
                "عرض جميع المنتجات",
                "البحث عن منتج",
                "إضافة منتج جديد",
                "تعديل منتج",
                "حذف منتج",
                "عرض تفاصيل منتج"
            ])
            
            choice = input("اختر من القائمة: ").strip()
            
            if choice == '1':
                self.view_all_products()
            elif choice == '2':
                self.search_products()
            elif choice == '3':
                self.add_product()
            elif choice == '4':
                self.edit_product()
            elif choice == '5':
                self.delete_product()
            elif choice == '6':
                self.view_product_details()
            elif choice == '0':
                break
            else:
                print(MESSAGES['invalid_choice'])
                pause()

    def view_all_products(self):
        """عرض جميع المنتجات"""
        clear_screen()
        products = self.product_manager.get_all_products()
        
        if not products:
            print("لا توجد منتجات")
            pause()
            return
        
        page = 1
        while True:
            clear_screen()
            self.product_manager.display_products(products, page)
            
            print("\nخيارات:")
            print("n - الصفحة التالية")
            print("p - الصفحة السابقة")
            print("0 - العودة")
            
            choice = input("اختر: ").strip().lower()
            
            if choice == 'n':
                total_pages = (len(products) + ITEMS_PER_PAGE - 1) // ITEMS_PER_PAGE
                if page < total_pages:
                    page += 1
            elif choice == 'p':
                if page > 1:
                    page -= 1
            elif choice == '0':
                break

    def search_products(self):
        """البحث عن المنتجات"""
        clear_screen()
        print_header("البحث عن المنتجات")
        
        search_term = get_user_input("أدخل كلمة البحث (الاسم، الباركود، الفئة، المورد)")
        if not search_term:
            return
        
        results = self.product_manager.search_products(search_term)
        
        if not results:
            print("لم يتم العثور على نتائج")
        else:
            print(f"\nتم العثور على {len(results)} منتج:")
            self.product_manager.display_products(results)
        
        pause()

    def add_product(self):
        """إضافة منتج جديد"""
        clear_screen()
        print_header("إضافة منتج جديد")
        
        try:
            name = get_user_input("اسم المنتج")
            if not name:
                return
            
            barcode = get_user_input("الباركود", required=False) or ""
            category = get_user_input("الفئة", required=False) or ""
            price = get_user_input("سعر البيع", float)
            cost = get_user_input("سعر التكلفة", float)
            supplier = get_user_input("المورد", required=False) or ""
            description = get_user_input("الوصف", required=False) or ""
            
            product = Product(
                name=name,
                barcode=barcode,
                category=category,
                price=price,
                cost=cost,
                supplier=supplier,
                description=description
            )
            
            product_id = self.product_manager.add_product(product)
            
            # إضافة المنتج للمخزون
            add_to_inventory = input("هل تريد إضافة المنتج للمخزون؟ (y/n): ").strip().lower()
            if add_to_inventory == 'y':
                quantity = get_user_input("الكمية الأولية", int) or 0
                min_stock = get_user_input("الحد الأدنى للمخزون", int) or 0
                reorder_point = get_user_input("نقطة إعادة الطلب", int) or 0
                location = get_user_input("الموقع", required=False) or ""
                
                self.inventory_manager.add_product_to_inventory(
                    product_id, quantity, min_stock, 0, reorder_point, location
                )
            
            print(MESSAGES['product_added'])
            
        except ValueError as e:
            print(f"خطأ: {e}")
        
        pause()

    def edit_product(self):
        """تعديل منتج"""
        clear_screen()
        print_header("تعديل منتج")
        
        # البحث عن المنتج
        search_term = get_user_input("أدخل معرف المنتج أو الباركود أو الاسم")
        if not search_term:
            return
        
        product = None
        
        # محاولة البحث بالمعرف
        try:
            product_id = int(search_term)
            product = self.product_manager.get_product(product_id)
        except ValueError:
            # البحث بالباركود
            product = self.product_manager.get_product_by_barcode(search_term)
            if not product:
                # البحث بالاسم
                results = self.product_manager.search_products(search_term)
                if len(results) == 1:
                    product = results[0]
                elif len(results) > 1:
                    print("تم العثور على عدة منتجات:")
                    for i, p in enumerate(results, 1):
                        print(f"{i}. {p.name} (معرف: {p.id})")
                    
                    choice = get_user_input("اختر رقم المنتج", int)
                    if 1 <= choice <= len(results):
                        product = results[choice - 1]
        
        if not product:
            print("المنتج غير موجود")
            pause()
            return
        
        # عرض بيانات المنتج الحالية
        self.product_manager.display_product_details(product)
        
        print("\nاتركه فارغاً للاحتفاظ بالقيمة الحالية")
        
        try:
            updates = {}
            
            new_name = get_user_input(f"الاسم الجديد (الحالي: {product.name})", required=False)
            if new_name:
                updates['name'] = new_name
            
            new_barcode = get_user_input(f"الباركود الجديد (الحالي: {product.barcode})", required=False)
            if new_barcode:
                updates['barcode'] = new_barcode
            
            new_category = get_user_input(f"الفئة الجديدة (الحالية: {product.category})", required=False)
            if new_category:
                updates['category'] = new_category
            
            new_price = input(f"السعر الجديد (الحالي: {product.price}): ").strip()
            if new_price:
                updates['price'] = validate_positive_number(new_price, "السعر")
            
            new_cost = input(f"التكلفة الجديدة (الحالية: {product.cost}): ").strip()
            if new_cost:
                updates['cost'] = validate_positive_number(new_cost, "التكلفة")
            
            new_supplier = get_user_input(f"المورد الجديد (الحالي: {product.supplier})", required=False)
            if new_supplier:
                updates['supplier'] = new_supplier
            
            new_description = get_user_input(f"الوصف الجديد (الحالي: {product.description})", required=False)
            if new_description:
                updates['description'] = new_description
            
            if updates:
                self.product_manager.update_product(product.id, **updates)
                print(MESSAGES['product_updated'])
            else:
                print("لم يتم إجراء أي تغييرات")
                
        except ValueError as e:
            print(f"خطأ: {e}")
        
        pause()

    def delete_product(self):
        """حذف منتج"""
        clear_screen()
        print_header("حذف منتج")

        product_id = get_user_input("معرف المنتج المراد حذفه", int)
        if not product_id:
            return

        product = self.product_manager.get_product(product_id)
        if not product:
            print("المنتج غير موجود")
            pause()
            return

        self.product_manager.display_product_details(product)

        confirm = input("هل أنت متأكد من حذف هذا المنتج؟ (y/n): ").strip().lower()
        if confirm == 'y':
            try:
                self.product_manager.delete_product(product_id)
                print(MESSAGES['product_deleted'])
            except ValueError as e:
                print(f"خطأ: {e}")

        pause()

    def view_product_details(self):
        """عرض تفاصيل منتج"""
        clear_screen()
        print_header("تفاصيل المنتج")

        search_term = get_user_input("أدخل معرف المنتج أو الباركود")
        if not search_term:
            return

        product = None
        try:
            product_id = int(search_term)
            product = self.product_manager.get_product(product_id)
        except ValueError:
            product = self.product_manager.get_product_by_barcode(search_term)

        if not product:
            print("المنتج غير موجود")
        else:
            self.product_manager.display_product_details(product)

            # عرض معلومات المخزون
            print_separator()
            self.inventory_manager.display_inventory_details(product.id)

        pause()

    def inventory_menu(self):
        """قائمة إدارة المخزون"""
        while True:
            clear_screen()
            display_menu("إدارة المخزون", [
                "عرض المخزون",
                "إضافة مخزون",
                "تحديث المخزون",
                "تقرير المخزون المنخفض",
                "تقرير المخزون النافد",
                "قيمة المخزون الإجمالية"
            ])

            choice = input("اختر من القائمة: ").strip()

            if choice == '1':
                self.view_inventory()
            elif choice == '2':
                self.add_stock()
            elif choice == '3':
                self.update_inventory()
            elif choice == '4':
                self.low_stock_report()
            elif choice == '5':
                self.out_of_stock_report()
            elif choice == '6':
                self.inventory_value_report()
            elif choice == '0':
                break
            else:
                print(MESSAGES['invalid_choice'])
                pause()

    def view_inventory(self):
        """عرض المخزون"""
        clear_screen()
        page = 1
        while True:
            clear_screen()
            self.inventory_manager.display_inventory(page)

            print("\nخيارات:")
            print("n - الصفحة التالية")
            print("p - الصفحة السابقة")
            print("0 - العودة")

            choice = input("اختر: ").strip().lower()

            if choice == 'n':
                page += 1
            elif choice == 'p':
                if page > 1:
                    page -= 1
            elif choice == '0':
                break

    def add_stock(self):
        """إضافة مخزون"""
        clear_screen()
        print_header("إضافة مخزون")

        try:
            product_id = get_user_input("معرف المنتج", int)
            if not product_id:
                return

            product = self.product_manager.get_product(product_id)
            if not product:
                print("المنتج غير موجود")
                pause()
                return

            print(f"المنتج: {product.name}")
            current_stock = self.inventory_manager.get_stock_quantity(product_id)
            print(f"المخزون الحالي: {current_stock}")

            quantity = get_user_input("الكمية المراد إضافتها", int)
            if quantity <= 0:
                print("الكمية يجب أن تكون أكبر من صفر")
                pause()
                return

            self.inventory_manager.add_stock(product_id, quantity)
            new_stock = self.inventory_manager.get_stock_quantity(product_id)
            print(f"تم إضافة {quantity} قطعة")
            print(f"المخزون الجديد: {new_stock}")

        except ValueError as e:
            print(f"خطأ: {e}")

        pause()

    def customers_menu(self):
        """قائمة إدارة العملاء"""
        while True:
            clear_screen()
            display_menu("إدارة العملاء", [
                "عرض جميع العملاء",
                "البحث عن عميل",
                "إضافة عميل جديد",
                "تعديل عميل",
                "حذف عميل",
                "عرض تفاصيل عميل",
                "أفضل العملاء"
            ])

            choice = input("اختر من القائمة: ").strip()

            if choice == '1':
                self.view_all_customers()
            elif choice == '2':
                self.search_customers()
            elif choice == '3':
                self.add_customer()
            elif choice == '4':
                self.edit_customer()
            elif choice == '5':
                self.delete_customer()
            elif choice == '6':
                self.view_customer_details()
            elif choice == '7':
                self.top_customers_report()
            elif choice == '0':
                break
            else:
                print(MESSAGES['invalid_choice'])
                pause()

    def sales_menu(self):
        """قائمة المبيعات"""
        while True:
            clear_screen()
            display_menu("المبيعات", [
                "بيع جديد",
                "عرض البيع الحالي",
                "إتمام البيع",
                "إلغاء البيع",
                "البحث عن فاتورة",
                "مبيعات اليوم"
            ])

            choice = input("اختر من القائمة: ").strip()

            if choice == '1':
                self.start_new_sale()
            elif choice == '2':
                self.view_current_sale()
            elif choice == '3':
                self.complete_sale()
            elif choice == '4':
                self.cancel_sale()
            elif choice == '5':
                self.search_sale()
            elif choice == '6':
                self.daily_sales()
            elif choice == '0':
                break
            else:
                print(MESSAGES['invalid_choice'])
                pause()

    def start_new_sale(self):
        """بدء بيع جديد"""
        clear_screen()
        print_header("بيع جديد")

        # اختيار العميل (اختياري)
        customer_choice = input("هل تريد ربط البيع بعميل؟ (y/n): ").strip().lower()
        customer_id = None

        if customer_choice == 'y':
            customer_search = get_user_input("أدخل معرف العميل أو رقم الهاتف", required=False)
            if customer_search:
                try:
                    customer_id = int(customer_search)
                    customer = self.customer_manager.get_customer(customer_id)
                except ValueError:
                    customer = self.customer_manager.get_customer_by_phone(customer_search)
                    if customer:
                        customer_id = customer.id

                if customer_id and self.customer_manager.get_customer(customer_id):
                    print(f"تم ربط البيع بالعميل: {self.customer_manager.get_customer(customer_id).name}")
                else:
                    print("العميل غير موجود")
                    customer_id = None

        cashier_name = get_user_input("اسم الكاشير", required=False) or "غير محدد"

        self.sales_manager.start_new_sale(customer_id, cashier_name)
        print("تم بدء عملية بيع جديدة")

        # إضافة المنتجات
        self.add_items_to_sale()

    def add_items_to_sale(self):
        """إضافة منتجات للبيع"""
        while True:
            clear_screen()
            self.sales_manager.display_current_sale()

            print("\nخيارات:")
            print("1. إضافة منتج")
            print("2. تعديل كمية منتج")
            print("3. حذف منتج")
            print("0. العودة للقائمة الرئيسية")

            choice = input("اختر: ").strip()

            if choice == '1':
                self.add_item_to_sale()
            elif choice == '2':
                self.update_item_quantity()
            elif choice == '3':
                self.remove_item_from_sale()
            elif choice == '0':
                break

    def add_item_to_sale(self):
        """إضافة منتج للبيع"""
        try:
            search_term = get_user_input("أدخل معرف المنتج أو الباركود")
            if not search_term:
                return

            product = None
            try:
                product_id = int(search_term)
                product = self.product_manager.get_product(product_id)
            except ValueError:
                product = self.product_manager.get_product_by_barcode(search_term)

            if not product:
                print("المنتج غير موجود")
                pause()
                return

            # عرض معلومات المنتج
            print(f"المنتج: {product.name}")
            print(f"السعر: {format_currency(product.price)}")

            available_stock = self.inventory_manager.get_stock_quantity(product.id)
            print(f"المخزون المتاح: {available_stock}")

            if available_stock <= 0:
                print("المنتج غير متوفر في المخزون")
                pause()
                return

            quantity = get_user_input("الكمية", int)
            if quantity <= 0:
                print("الكمية يجب أن تكون أكبر من صفر")
                pause()
                return

            if quantity > available_stock:
                print(f"الكمية المطلوبة أكبر من المخزون المتاح ({available_stock})")
                pause()
                return

            self.sales_manager.add_item_to_current_sale(product.id, quantity)
            print(f"تم إضافة {quantity} من {product.name}")

        except ValueError as e:
            print(f"خطأ: {e}")

        pause()

    def complete_sale(self):
        """إتمام البيع"""
        if not self.sales_manager.current_sale:
            print("لا توجد عملية بيع نشطة")
            pause()
            return

        if not self.sales_manager.current_sale.items:
            print("لا توجد منتجات في البيع")
            pause()
            return

        clear_screen()
        self.sales_manager.display_current_sale()

        print_separator()
        print("إتمام البيع")
        print_separator()

        total_amount = self.sales_manager.current_sale.total_amount
        print(f"الإجمالي المطلوب: {format_currency(total_amount)}")

        # اختيار طريقة الدفع
        print("\nطرق الدفع:")
        print("1. نقدي")
        print("2. بطاقة")
        print("3. نقاط")

        payment_choice = input("اختر طريقة الدفع: ").strip()
        payment_methods = {'1': 'نقدي', '2': 'بطاقة', '3': 'نقاط'}
        payment_method = payment_methods.get(payment_choice, 'نقدي')

        points_used = 0

        # استخدام النقاط إذا كان العميل مسجلاً
        if self.sales_manager.current_sale.customer_id:
            customer = self.customer_manager.get_customer(self.sales_manager.current_sale.customer_id)
            if customer and customer.points > 0:
                use_points = input(f"لديك {customer.points} نقطة. هل تريد استخدامها؟ (y/n): ").strip().lower()
                if use_points == 'y':
                    max_points = min(customer.points, int(total_amount))
                    points_used = get_user_input(f"عدد النقاط المراد استخدامها (الحد الأقصى: {max_points})", int) or 0
                    points_used = min(points_used, max_points)

        # حساب المبلغ بعد خصم النقاط
        final_amount = max(0, total_amount - points_used)

        if final_amount > 0:
            paid_amount = get_user_input(f"المبلغ المدفوع (المطلوب: {format_currency(final_amount)})", float)

            if paid_amount < final_amount:
                print("المبلغ المدفوع أقل من المطلوب")
                pause()
                return
        else:
            paid_amount = 0

        try:
            completed_sale = self.sales_manager.complete_current_sale(paid_amount, payment_method, points_used)

            # طباعة الفاتورة
            clear_screen()
            print("تم إتمام البيع بنجاح!")
            print_separator()
            self.receipt_generator.print_receipt(completed_sale)

            # حفظ الفاتورة
            try:
                filename = self.receipt_generator.save_receipt_to_file(completed_sale)
                print(f"\nتم حفظ الفاتورة في: {filename}")
            except Exception as e:
                print(f"تعذر حفظ الفاتورة: {e}")

        except ValueError as e:
            print(f"خطأ: {e}")

        pause()

    def reports_menu(self):
        """قائمة التقارير"""
        while True:
            clear_screen()
            display_menu("التقارير", [
                "تقرير المبيعات اليومي",
                "تقرير المبيعات الشهري",
                "تقرير أفضل المنتجات",
                "تقرير أفضل العملاء",
                "تقرير المخزون المنخفض",
                "إحصائيات عامة"
            ])

            choice = input("اختر من القائمة: ").strip()

            if choice == '1':
                self.daily_sales_report()
            elif choice == '2':
                self.monthly_sales_report()
            elif choice == '3':
                self.top_products_report()
            elif choice == '4':
                self.top_customers_report()
            elif choice == '5':
                self.low_stock_report()
            elif choice == '6':
                self.general_statistics()
            elif choice == '0':
                break
            else:
                print(MESSAGES['invalid_choice'])
                pause()

    def daily_sales_report(self):
        """تقرير المبيعات اليومي"""
        clear_screen()
        print_header("تقرير المبيعات اليومي")

        date_str = get_user_input("أدخل التاريخ (YYYY-MM-DD) أو اتركه فارغاً لليوم الحالي", required=False)
        if not date_str:
            date_str = get_current_timestamp()[:10]

        daily_sales = self.sales_manager.get_sales_by_date(date_str)

        if not daily_sales:
            print(f"لا توجد مبيعات في تاريخ {date_str}")
        else:
            report_text = self.receipt_generator.generate_daily_report(daily_sales, date_str)
            print(report_text)

            # حفظ التقرير
            save_report = input("\nهل تريد حفظ التقرير؟ (y/n): ").strip().lower()
            if save_report == 'y':
                try:
                    filename = self.receipt_generator.save_daily_report(daily_sales, date_str)
                    print(f"تم حفظ التقرير في: {filename}")
                except Exception as e:
                    print(f"تعذر حفظ التقرير: {e}")

        pause()

    # الوظائف المفقودة
    def update_inventory(self):
        """تحديث المخزون"""
        clear_screen()
        print_header("تحديث المخزون")

        try:
            product_id = get_user_input("معرف المنتج", int)
            if not product_id:
                return

            product = self.product_manager.get_product(product_id)
            if not product:
                print("المنتج غير موجود")
                pause()
                return

            self.inventory_manager.display_inventory_details(product_id)

            print("\nاتركه فارغاً للاحتفاظ بالقيمة الحالية")

            updates = {}

            new_min_stock = input("الحد الأدنى الجديد: ").strip()
            if new_min_stock:
                updates['min_stock'] = validate_positive_integer(new_min_stock, "الحد الأدنى")

            new_reorder_point = input("نقطة إعادة الطلب الجديدة: ").strip()
            if new_reorder_point:
                updates['reorder_point'] = validate_positive_integer(new_reorder_point, "نقطة إعادة الطلب")

            new_location = get_user_input("الموقع الجديد", required=False)
            if new_location:
                updates['location'] = new_location

            if updates:
                self.inventory_manager.update_inventory_item(product_id, **updates)
                print("تم تحديث المخزون بنجاح")
            else:
                print("لم يتم إجراء أي تغييرات")

        except ValueError as e:
            print(f"خطأ: {e}")

        pause()

    def low_stock_report(self):
        """تقرير المخزون المنخفض"""
        clear_screen()
        self.inventory_manager.display_low_stock_report()
        pause()

    def out_of_stock_report(self):
        """تقرير المخزون النافد"""
        clear_screen()
        out_of_stock_items = self.inventory_manager.get_out_of_stock_items()

        if not out_of_stock_items:
            print("لا توجد منتجات نافدة من المخزون")
        else:
            print_header("تقرير المنتجات نافدة المخزون")
            print(f"{'المعرف':<5} {'الاسم':<20} {'الكمية':<10}")
            print_separator()

            for product, item in out_of_stock_items:
                print(f"{product.id:<5} {product.name:<20} {item.quantity:<10}")

        pause()

    def inventory_value_report(self):
        """تقرير قيمة المخزون"""
        clear_screen()
        print_header("قيمة المخزون الإجمالية")

        total_value = self.inventory_manager.get_inventory_value()
        print(f"قيمة المخزون الإجمالية: {format_currency(total_value)}")

        pause()

    def view_all_customers(self):
        """عرض جميع العملاء"""
        clear_screen()
        customers = self.customer_manager.get_all_customers()

        if not customers:
            print("لا يوجد عملاء")
            pause()
            return

        page = 1
        while True:
            clear_screen()
            self.customer_manager.display_customers(customers, page)

            print("\nخيارات:")
            print("n - الصفحة التالية")
            print("p - الصفحة السابقة")
            print("0 - العودة")

            choice = input("اختر: ").strip().lower()

            if choice == 'n':
                total_pages = (len(customers) + ITEMS_PER_PAGE - 1) // ITEMS_PER_PAGE
                if page < total_pages:
                    page += 1
            elif choice == 'p':
                if page > 1:
                    page -= 1
            elif choice == '0':
                break

    def search_customers(self):
        """البحث عن العملاء"""
        clear_screen()
        print_header("البحث عن العملاء")

        search_term = get_user_input("أدخل كلمة البحث (الاسم، الهاتف، البريد، النوع)")
        if not search_term:
            return

        results = self.customer_manager.search_customers(search_term)

        if not results:
            print("لم يتم العثور على نتائج")
        else:
            print(f"\nتم العثور على {len(results)} عميل:")
            self.customer_manager.display_customers(results)

        pause()

    def add_customer(self):
        """إضافة عميل جديد"""
        clear_screen()
        print_header("إضافة عميل جديد")

        try:
            name = get_user_input("اسم العميل")
            if not name:
                return

            phone = get_user_input("رقم الهاتف", required=False) or ""
            email = get_user_input("البريد الإلكتروني", required=False) or ""
            address = get_user_input("العنوان", required=False) or ""

            print("\nأنواع العملاء:")
            print("1. عادي")
            print("2. VIP")
            print("3. جملة")

            type_choice = input("اختر نوع العميل (1-3): ").strip()
            customer_types = {'1': 'عادي', '2': 'VIP', '3': 'جملة'}
            customer_type = customer_types.get(type_choice, 'عادي')

            discount_rate = 0.0
            if customer_type in ['VIP', 'جملة']:
                discount_rate = get_user_input("معدل الخصم (%)", float) or 0.0

            customer = Customer(
                name=name,
                phone=phone,
                email=email,
                address=address,
                customer_type=customer_type,
                discount_rate=discount_rate
            )

            customer_id = self.customer_manager.add_customer(customer)
            print(f"تم إضافة العميل بنجاح. المعرف: {customer_id}")

        except ValueError as e:
            print(f"خطأ: {e}")

        pause()

    def view_current_sale(self):
        """عرض البيع الحالي"""
        clear_screen()
        self.sales_manager.display_current_sale()
        pause()

    def cancel_sale(self):
        """إلغاء البيع الحالي"""
        if not self.sales_manager.current_sale:
            print("لا توجد عملية بيع نشطة")
            pause()
            return

        confirm = input("هل أنت متأكد من إلغاء البيع الحالي؟ (y/n): ").strip().lower()
        if confirm == 'y':
            self.sales_manager.cancel_current_sale()
            print("تم إلغاء البيع")

        pause()

    def update_item_quantity(self):
        """تعديل كمية منتج في البيع"""
        if not self.sales_manager.current_sale or not self.sales_manager.current_sale.items:
            print("لا توجد منتجات في البيع")
            pause()
            return

        try:
            product_id = get_user_input("معرف المنتج", int)
            if not product_id:
                return

            # البحث عن المنتج في البيع
            item_found = False
            for item in self.sales_manager.current_sale.items:
                if item.product_id == product_id:
                    item_found = True
                    print(f"المنتج: {item.product_name}")
                    print(f"الكمية الحالية: {item.quantity}")
                    break

            if not item_found:
                print("المنتج غير موجود في البيع")
                pause()
                return

            new_quantity = get_user_input("الكمية الجديدة", int)
            if new_quantity < 0:
                print("الكمية يجب أن تكون أكبر من أو تساوي صفر")
                pause()
                return

            # التحقق من المخزون
            if new_quantity > 0:
                available_stock = self.inventory_manager.get_stock_quantity(product_id)
                if new_quantity > available_stock:
                    print(f"الكمية المطلوبة أكبر من المخزون المتاح ({available_stock})")
                    pause()
                    return

            self.sales_manager.current_sale.update_item_quantity(product_id, new_quantity)
            print("تم تحديث الكمية بنجاح")

        except ValueError as e:
            print(f"خطأ: {e}")

        pause()

    def remove_item_from_sale(self):
        """حذف منتج من البيع"""
        if not self.sales_manager.current_sale or not self.sales_manager.current_sale.items:
            print("لا توجد منتجات في البيع")
            pause()
            return

        try:
            product_id = get_user_input("معرف المنتج المراد حذفه", int)
            if not product_id:
                return

            # البحث عن المنتج في البيع
            item_found = False
            for item in self.sales_manager.current_sale.items:
                if item.product_id == product_id:
                    item_found = True
                    print(f"المنتج: {item.product_name}")
                    break

            if not item_found:
                print("المنتج غير موجود في البيع")
                pause()
                return

            confirm = input("هل أنت متأكد من حذف هذا المنتج؟ (y/n): ").strip().lower()
            if confirm == 'y':
                self.sales_manager.current_sale.remove_item(product_id)
                print("تم حذف المنتج من البيع")

        except ValueError as e:
            print(f"خطأ: {e}")

        pause()

    def edit_customer(self):
        """تعديل عميل"""
        clear_screen()
        print_header("تعديل عميل")

        search_term = get_user_input("أدخل معرف العميل أو رقم الهاتف")
        if not search_term:
            return

        customer = None
        try:
            customer_id = int(search_term)
            customer = self.customer_manager.get_customer(customer_id)
        except ValueError:
            customer = self.customer_manager.get_customer_by_phone(search_term)

        if not customer:
            print("العميل غير موجود")
            pause()
            return

        self.customer_manager.display_customer_details(customer)

        print("\nاتركه فارغاً للاحتفاظ بالقيمة الحالية")

        try:
            updates = {}

            new_name = get_user_input(f"الاسم الجديد (الحالي: {customer.name})", required=False)
            if new_name:
                updates['name'] = new_name

            new_phone = get_user_input(f"الهاتف الجديد (الحالي: {customer.phone})", required=False)
            if new_phone:
                updates['phone'] = new_phone

            new_email = get_user_input(f"البريد الجديد (الحالي: {customer.email})", required=False)
            if new_email:
                updates['email'] = new_email

            new_address = get_user_input(f"العنوان الجديد (الحالي: {customer.address})", required=False)
            if new_address:
                updates['address'] = new_address

            new_discount = input(f"معدل الخصم الجديد (الحالي: {customer.discount_rate}%): ").strip()
            if new_discount:
                updates['discount_rate'] = validate_positive_number(new_discount, "معدل الخصم")

            if updates:
                self.customer_manager.update_customer(customer.id, **updates)
                print("تم تحديث العميل بنجاح")
            else:
                print("لم يتم إجراء أي تغييرات")

        except ValueError as e:
            print(f"خطأ: {e}")

        pause()

    def delete_customer(self):
        """حذف عميل"""
        clear_screen()
        print_header("حذف عميل")

        customer_id = get_user_input("معرف العميل المراد حذفه", int)
        if not customer_id:
            return

        customer = self.customer_manager.get_customer(customer_id)
        if not customer:
            print("العميل غير موجود")
            pause()
            return

        self.customer_manager.display_customer_details(customer)

        confirm = input("هل أنت متأكد من حذف هذا العميل؟ (y/n): ").strip().lower()
        if confirm == 'y':
            try:
                self.customer_manager.delete_customer(customer_id)
                print("تم حذف العميل بنجاح")
            except ValueError as e:
                print(f"خطأ: {e}")

        pause()

    def view_customer_details(self):
        """عرض تفاصيل عميل"""
        clear_screen()
        print_header("تفاصيل العميل")

        search_term = get_user_input("أدخل معرف العميل أو رقم الهاتف")
        if not search_term:
            return

        customer = None
        try:
            customer_id = int(search_term)
            customer = self.customer_manager.get_customer(customer_id)
        except ValueError:
            customer = self.customer_manager.get_customer_by_phone(search_term)

        if not customer:
            print("العميل غير موجود")
        else:
            self.customer_manager.display_customer_details(customer)

            # عرض مبيعات العميل
            customer_sales = self.sales_manager.get_sales_by_customer(customer.id)
            if customer_sales:
                print_separator()
                print(f"عدد الفواتير: {len(customer_sales)}")
                print("آخر 5 فواتير:")
                for sale in sorted(customer_sales, key=lambda s: s.created_at, reverse=True)[:5]:
                    print(f"  فاتورة {sale.id} - {sale.created_at[:16]} - {format_currency(sale.total_amount)}")

        pause()

    def top_customers_report(self):
        """تقرير أفضل العملاء"""
        clear_screen()
        self.customer_manager.display_top_customers_report()
        pause()

    def search_sale(self):
        """البحث عن فاتورة"""
        clear_screen()
        print_header("البحث عن فاتورة")

        sale_id = get_user_input("رقم الفاتورة", int)
        if not sale_id:
            return

        sale = self.sales_manager.get_sale(sale_id)
        if not sale:
            print("الفاتورة غير موجودة")
        else:
            self.receipt_generator.print_receipt(sale)

        pause()

    def daily_sales(self):
        """مبيعات اليوم"""
        clear_screen()
        print_header("مبيعات اليوم")

        today = get_current_timestamp()[:10]
        daily_sales = self.sales_manager.get_sales_by_date(today)

        if not daily_sales:
            print("لا توجد مبيعات اليوم")
        else:
            total_amount = sum(sale.total_amount for sale in daily_sales)
            print(f"عدد الفواتير: {len(daily_sales)}")
            print(f"إجمالي المبيعات: {format_currency(total_amount)}")

            print_separator()
            self.sales_manager.display_sales_report(today, today)

        pause()

    def monthly_sales_report(self):
        """تقرير المبيعات الشهري"""
        clear_screen()
        print_header("تقرير المبيعات الشهري")

        month_str = get_user_input("أدخل الشهر (YYYY-MM) أو اتركه فارغاً للشهر الحالي", required=False)
        if not month_str:
            month_str = get_current_timestamp()[:7]

        # الحصول على جميع المبيعات للشهر
        monthly_sales = []
        for sale in self.sales_manager.sales.values():
            if sale.created_at.startswith(month_str):
                monthly_sales.append(sale)

        if not monthly_sales:
            print(f"لا توجد مبيعات في شهر {month_str}")
        else:
            total_amount = sum(sale.total_amount for sale in monthly_sales)
            total_items = sum(len(sale.items) for sale in monthly_sales)

            print(f"عدد الفواتير: {len(monthly_sales)}")
            print(f"إجمالي العناصر: {total_items}")
            print(f"إجمالي المبيعات: {format_currency(total_amount)}")
            print(f"متوسط الفاتورة: {format_currency(total_amount / len(monthly_sales))}")

            # تقرير المنتجات الأكثر مبيعاً
            print_separator()
            product_report = self.receipt_generator.generate_product_sales_report(
                monthly_sales, f"أفضل المنتجات - {month_str}"
            )
            print(product_report)

        pause()

    def top_products_report(self):
        """تقرير أفضل المنتجات"""
        clear_screen()
        print_header("تقرير أفضل المنتجات")

        period = input("اختر الفترة (1-يومي، 2-شهري، 3-جميع الفترات): ").strip()

        sales_list = []
        if period == '1':
            # يومي
            date_str = get_current_timestamp()[:10]
            sales_list = self.sales_manager.get_sales_by_date(date_str)
            title = f"أفضل المنتجات - {date_str}"
        elif period == '2':
            # شهري
            month_str = get_current_timestamp()[:7]
            for sale in self.sales_manager.sales.values():
                if sale.created_at.startswith(month_str):
                    sales_list.append(sale)
            title = f"أفضل المنتجات - {month_str}"
        else:
            # جميع الفترات
            sales_list = list(self.sales_manager.sales.values())
            title = "أفضل المنتجات - جميع الفترات"

        if not sales_list:
            print("لا توجد مبيعات في الفترة المحددة")
        else:
            report = self.receipt_generator.generate_product_sales_report(sales_list, title)
            print(report)

        pause()

    def general_statistics(self):
        """إحصائيات عامة"""
        clear_screen()
        print_header("إحصائيات عامة")

        # إحصائيات المنتجات
        products = self.product_manager.get_all_products()
        categories = self.product_manager.get_all_categories()

        print("📦 المنتجات:")
        print(f"  إجمالي المنتجات: {len(products)}")
        print(f"  عدد الفئات: {len(categories)}")

        # إحصائيات المخزون
        total_inventory_value = self.inventory_manager.get_inventory_value()
        low_stock_items = self.inventory_manager.get_low_stock_items()
        out_of_stock_items = self.inventory_manager.get_out_of_stock_items()

        print("\n📊 المخزون:")
        print(f"  قيمة المخزون الإجمالية: {format_currency(total_inventory_value)}")
        print(f"  منتجات منخفضة المخزون: {len(low_stock_items)}")
        print(f"  منتجات نافدة: {len(out_of_stock_items)}")

        # إحصائيات العملاء
        customer_stats = self.customer_manager.get_customer_statistics()

        print("\n👥 العملاء:")
        print(f"  إجمالي العملاء: {customer_stats['total_customers']}")
        print(f"  عملاء VIP: {customer_stats['vip_customers']}")
        print(f"  عملاء عاديون: {customer_stats['regular_customers']}")
        if customer_stats['total_customers'] > 0:
            print(f"  متوسط المشتريات: {format_currency(customer_stats['average_purchase'])}")

        # إحصائيات المبيعات
        all_sales = list(self.sales_manager.sales.values())
        today_sales = self.sales_manager.get_sales_by_date(get_current_timestamp()[:10])

        print("\n💰 المبيعات:")
        print(f"  إجمالي الفواتير: {len(all_sales)}")
        print(f"  فواتير اليوم: {len(today_sales)}")

        if all_sales:
            total_sales_amount = sum(sale.total_amount for sale in all_sales)
            print(f"  إجمالي المبيعات: {format_currency(total_sales_amount)}")
            print(f"  متوسط الفاتورة: {format_currency(total_sales_amount / len(all_sales))}")

        if today_sales:
            today_total = sum(sale.total_amount for sale in today_sales)
            print(f"  مبيعات اليوم: {format_currency(today_total)}")

        pause()

if __name__ == "__main__":
    system = SupermarketSystem()
    system.run()
