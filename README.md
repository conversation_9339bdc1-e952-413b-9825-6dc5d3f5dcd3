# برنامج إدارة السوبر ماركت
## Supermarket Management System

برنامج شامل لإدارة السوبر ماركت مكتوب بلغة Python مع دعم العملة العراقية (الدينار العراقي).

## الميزات الرئيسية

### 🛍️ إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- تصنيف المنتجات حسب الفئات
- إدارة الباركود والأسعار
- حساب هامش الربح
- البحث في المنتجات

### 📦 إدارة المخزون
- تتبع كميات المخزون
- تحديد الحد الأدنى ونقطة إعادة الطلب
- تقارير المخزون المنخفض والنافد
- حساب قيمة المخزون الإجمالية
- إدارة مواقع التخزين

### 👥 إدارة العملاء
- تسجيل العملاء (عادي، VIP، جملة)
- نظام النقاط والخصومات
- تتبع مشتريات العملاء
- تقارير أفضل العملاء

### 💰 نظام المبيعات
- واجهة بيع سهلة الاستخدام
- دعم طرق دفع متعددة (نقدي، بطاقة، نقاط)
- حساب الضرائب والخصومات
- طباعة الفواتير
- إدارة المرتجعات

### 📊 التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- تقارير أفضل المنتجات والعملاء
- إحصائيات شاملة للنشاط التجاري
- تصدير التقارير

## متطلبات التشغيل

- Python 3.6 أو أحدث
- نظام التشغيل: Windows, Linux, macOS

## التثبيت والتشغيل

1. **تحميل الملفات:**
   ```bash
   # تأكد من وجود جميع الملفات في نفس المجلد
   main.py          # الملف الرئيسي
   config.py        # إعدادات البرنامج
   utils.py         # وظائف مساعدة
   product.py       # إدارة المنتجات
   inventory.py     # إدارة المخزون
   customer.py      # إدارة العملاء
   sale.py          # نظام المبيعات
   receipt.py       # نظام الفواتير
   run.py           # ملف التشغيل المحسن
   sample_data.py   # إنشاء بيانات تجريبية
   start.bat        # تشغيل على Windows
   start.sh         # تشغيل على Linux/Mac
   ```

2. **طرق التشغيل:**

   **الطريقة الأولى (مستحسنة):**
   ```bash
   # على Windows
   start.bat

   # على Linux/Mac
   chmod +x start.sh
   ./start.sh
   ```

   **الطريقة الثانية:**
   ```bash
   python run.py
   ```

   **الطريقة الثالثة (مباشرة):**
   ```bash
   python main.py
   ```

3. **إنشاء بيانات تجريبية (اختياري):**
   ```bash
   python sample_data.py
   ```

## هيكل البرنامج

```
📁 برنامج السوبر ماركت/
├── 📄 main.py              # الملف الرئيسي
├── 📄 config.py            # إعدادات البرنامج
├── 📄 utils.py             # وظائف مساعدة
├── 📄 product.py           # إدارة المنتجات
├── 📄 inventory.py         # إدارة المخزون
├── 📄 customer.py          # إدارة العملاء
├── 📄 sale.py              # نظام المبيعات
├── 📄 receipt.py           # نظام الفواتير
├── 📄 run.py               # ملف التشغيل المحسن
├── 📄 sample_data.py       # إنشاء بيانات تجريبية
├── 📄 test_program.py      # اختبار البرنامج
├── 📄 start.bat            # تشغيل على Windows
├── 📄 start.sh             # تشغيل على Linux/Mac
├── 📁 data/                # مجلد البيانات (ينشأ تلقائياً)
│   ├── 📄 products.json    # بيانات المنتجات
│   ├── 📄 customers.json   # بيانات العملاء
│   ├── 📄 sales.json       # بيانات المبيعات
│   ├── 📄 inventory.json   # بيانات المخزون
│   ├── 📁 receipts/        # الفواتير المحفوظة
│   └── 📁 reports/         # التقارير المحفوظة
└── 📄 README.md            # دليل الاستخدام
```

## دليل الاستخدام السريع

### 1. إضافة منتج جديد
1. اختر "إدارة المنتجات" من القائمة الرئيسية
2. اختر "إضافة منتج جديد"
3. أدخل بيانات المنتج (الاسم، السعر، التكلفة، إلخ)
4. اختر إضافة المنتج للمخزون إذا رغبت

### 2. إجراء عملية بيع
1. اختر "المبيعات" من القائمة الرئيسية
2. اختر "بيع جديد"
3. اربط البيع بعميل (اختياري)
4. أضف المنتجات بإدخال المعرف أو الباركود
5. اختر "إتمام البيع" وحدد طريقة الدفع

### 3. عرض التقارير
1. اختر "التقارير" من القائمة الرئيسية
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية إذا لزم الأمر

## الإعدادات

يمكن تخصيص البرنامج من خلال ملف `config.py`:

```python
# العملة
CURRENCY = "IQD"  # الدينار العراقي
CURRENCY_SYMBOL = "د.ع"

# معدل الضريبة
TAX_RATE = 0.0  # 0% (يمكن تعديلها)

# عدد العناصر في الصفحة
ITEMS_PER_PAGE = 10
```

## العملة والتنسيق

البرنامج يدعم الدينار العراقي كعملة افتراضية مع التنسيق المناسب:
- رمز العملة: د.ع
- تنسيق الأرقام: 1,000 د.ع
- دعم الأرقام الكبيرة

## نظام النقاط

- يحصل العميل على نقطة واحدة لكل 1000 دينار عراقي
- يمكن استخدام النقاط كخصم (نقطة = 1 دينار)
- النقاط تتراكم مع كل عملية شراء

## أنواع العملاء

1. **عادي**: عميل عادي بدون خصومات خاصة
2. **VIP**: عميل مميز مع خصم محدد
3. **جملة**: عميل جملة مع خصم أكبر

## النسخ الاحتياطي

البيانات تحفظ تلقائياً في ملفات JSON في مجلد `data/`. يُنصح بعمل نسخة احتياطية دورية من هذا المجلد.

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في تشغيل البرنامج:**
   - تأكد من تثبيت Python 3.6+
   - تأكد من وجود جميع الملفات

2. **فقدان البيانات:**
   - تحقق من مجلد `data/`
   - تأكد من صلاحيات الكتابة

3. **مشاكل في الترميز:**
   - تأكد من دعم UTF-8 في terminal

## المساهمة والتطوير

البرنامج مفتوح للتطوير والتحسين. يمكن إضافة ميزات جديدة مثل:
- واجهة رسومية (GUI)
- قاعدة بيانات SQL
- تكامل مع أنظمة دفع إلكترونية
- تقارير أكثر تفصيلاً
- دعم عملات متعددة

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**ملاحظة:** هذا البرنامج مصمم للاستخدام التعليمي والتجاري الصغير. للاستخدام التجاري الكبير، يُنصح بإضافة ميزات أمان إضافية وقاعدة بيانات أكثر قوة.
