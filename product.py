# فئة المنتجات
# Product class for supermarket system

from utils import *
from config import *

class Product:
    def __init__(self, product_id=None, name="", barcode="", category="", 
                 price=0.0, cost=0.0, description="", supplier=""):
        self.id = product_id
        self.name = name
        self.barcode = barcode
        self.category = category
        self.price = price  # سعر البيع
        self.cost = cost    # سعر التكلفة
        self.description = description
        self.supplier = supplier
        self.created_at = get_current_timestamp()
        self.updated_at = get_current_timestamp()

    def to_dict(self):
        """تحويل المنتج إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'barcode': self.barcode,
            'category': self.category,
            'price': self.price,
            'cost': self.cost,
            'description': self.description,
            'supplier': self.supplier,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    @classmethod
    def from_dict(cls, data):
        """إنشاء منتج من قاموس"""
        product = cls()
        product.id = data.get('id')
        product.name = data.get('name', '')
        product.barcode = data.get('barcode', '')
        product.category = data.get('category', '')
        product.price = data.get('price', 0.0)
        product.cost = data.get('cost', 0.0)
        product.description = data.get('description', '')
        product.supplier = data.get('supplier', '')
        product.created_at = data.get('created_at', get_current_timestamp())
        product.updated_at = data.get('updated_at', get_current_timestamp())
        return product

    def update(self, **kwargs):
        """تحديث بيانات المنتج"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = get_current_timestamp()

    def get_profit_margin(self):
        """حساب هامش الربح"""
        if self.cost > 0:
            return ((self.price - self.cost) / self.cost) * 100
        return 0

    def __str__(self):
        return f"{self.name} - {format_currency(self.price)}"

class ProductManager:
    def __init__(self):
        self.products = {}
        self.load_products()

    def load_products(self):
        """تحميل المنتجات من الملف"""
        data = load_json_file(PRODUCTS_FILE)
        self.products = {}
        for product_data in data.values():
            product = Product.from_dict(product_data)
            self.products[product.id] = product

    def save_products(self):
        """حفظ المنتجات في الملف"""
        data = {}
        for product_id, product in self.products.items():
            data[str(product_id)] = product.to_dict()
        save_json_file(PRODUCTS_FILE, data)

    def add_product(self, product):
        """إضافة منتج جديد"""
        if product.id is None:
            product.id = generate_id(list(self.products.keys()))
        
        # التحقق من عدم تكرار الباركود
        if product.barcode and self.get_product_by_barcode(product.barcode):
            raise ValueError("الباركود موجود مسبقاً")
        
        self.products[product.id] = product
        self.save_products()
        return product.id

    def update_product(self, product_id, **kwargs):
        """تحديث منتج"""
        if product_id not in self.products:
            raise ValueError("المنتج غير موجود")
        
        # التحقق من الباركود عند التحديث
        if 'barcode' in kwargs and kwargs['barcode']:
            existing_product = self.get_product_by_barcode(kwargs['barcode'])
            if existing_product and existing_product.id != product_id:
                raise ValueError("الباركود موجود مسبقاً")
        
        self.products[product_id].update(**kwargs)
        self.save_products()

    def delete_product(self, product_id):
        """حذف منتج"""
        if product_id not in self.products:
            raise ValueError("المنتج غير موجود")
        
        del self.products[product_id]
        self.save_products()

    def get_product(self, product_id):
        """الحصول على منتج بالمعرف"""
        return self.products.get(product_id)

    def get_product_by_barcode(self, barcode):
        """الحصول على منتج بالباركود"""
        for product in self.products.values():
            if product.barcode == barcode:
                return product
        return None

    def search_products(self, search_term):
        """البحث في المنتجات"""
        products_list = list(self.products.values())
        search_fields = ['name', 'barcode', 'category', 'supplier']
        return search_items(products_list, search_term, search_fields)

    def get_products_by_category(self, category):
        """الحصول على المنتجات حسب الفئة"""
        return [p for p in self.products.values() if p.category.lower() == category.lower()]

    def get_all_products(self):
        """الحصول على جميع المنتجات"""
        return list(self.products.values())

    def get_all_categories(self):
        """الحصول على جميع الفئات"""
        categories = set()
        for product in self.products.values():
            if product.category:
                categories.add(product.category)
        return sorted(list(categories))

    def display_products(self, products=None, page=1):
        """عرض المنتجات"""
        if products is None:
            products = self.get_all_products()
        
        if not products:
            print("لا توجد منتجات للعرض")
            return
        
        paginated = paginate_items(products, page)
        
        print(f"\nالمنتجات (الصفحة {paginated['current_page']} من {paginated['total_pages']})")
        print(f"إجمالي المنتجات: {paginated['total_items']}")
        print_separator()
        
        print(f"{'المعرف':<5} {'الاسم':<20} {'الفئة':<15} {'السعر':<15} {'الباركود':<15}")
        print_separator()
        
        for product in paginated['items']:
            print(f"{product.id:<5} {product.name:<20} {product.category:<15} "
                  f"{format_currency(product.price):<15} {product.barcode:<15}")
        
        if paginated['total_pages'] > 1:
            print(f"\nالصفحة {paginated['current_page']} من {paginated['total_pages']}")

    def display_product_details(self, product):
        """عرض تفاصيل المنتج"""
        print_header("تفاصيل المنتج")
        print(f"المعرف: {product.id}")
        print(f"الاسم: {product.name}")
        print(f"الباركود: {product.barcode}")
        print(f"الفئة: {product.category}")
        print(f"سعر البيع: {format_currency(product.price)}")
        print(f"سعر التكلفة: {format_currency(product.cost)}")
        print(f"هامش الربح: {product.get_profit_margin():.1f}%")
        print(f"المورد: {product.supplier}")
        print(f"الوصف: {product.description}")
        print(f"تاريخ الإنشاء: {product.created_at}")
        print(f"تاريخ التحديث: {product.updated_at}")
